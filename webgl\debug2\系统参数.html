<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统参数 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 系统参数专用样式 -->
    <style>
        /* 系统参数专用样式扩展 */
        .system-param-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0;
            margin-bottom: 15px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            position: relative;
            padding-bottom: 70px;
            overflow: hidden;
            min-height: 0;
        }

        /* 控件容器样式 - 统一所有控件的容器 */
        .control-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 0;
        }

        /* 浮点数输入框样式 */
        .float-input {
            width: 100px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .float-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .float-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        /* 单面板布局样式 */
        .main-layout {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 120px);
            max-height: calc(100vh - 120px);
        }

        .single-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
        }

        .system-param-panel {
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 10px;
            overflow: hidden;
            min-width: 0;
            display: flex;
            flex-direction: column;
            flex: 1;
            min-height: 0;
        }

        /* 表格容器样式 */
        .table-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }

        /* 面板标题优化 */
        .panel-title {
            font-size: 16px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 8px;
            padding: 6px 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* 表格样式优化 - 支持每行四个参数 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 6px 3px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.4);
            font-weight: bold;
            font-size: 12px;
        }

        .params-table td {
            padding: 3px 2px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(42, 49, 66, 0.7);
            vertical-align: middle;
            height: 50px;
        }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        /* 序号列样式 */
        .param-index {
            font-size: 12px;
            color: #7a8ba0;
            font-weight: bold;
            width: 30px;
            text-align: center;
        }

        /* 参数名称列样式 */
        .param-name {
            font-size: 12px;
            color: #ffffff;
            text-align: left;
            padding-left: 4px;
            line-height: 1.2;
            max-width: 120px;
            width: 120px;
            word-wrap: break-word;
            overflow: visible;
            white-space: normal;
        }

        /* 当前值列样式 */
        .param-current {
            font-size: 12px;
            color: #00d4ff;
            font-weight: bold;
            width: 80px;
            text-align: center;
        }

        /* 设定值列样式 */
        .param-setting {
            width: 110px;
            text-align: center;
            padding: 0 5px;
        }

        /* 单位列样式 - 已移除 */
        /* .param-unit {
            font-size: 11px;
            color: #7a8ba0;
            width: 40px;
            text-align: center;
        } */
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>系统参数</h1>
        </div>

        <div class="main-content">
            <div class="main-layout">
                <!-- 单面板：系统参数 -->
                <div class="single-panel">
                    <div class="system-param-panel">
                        <div class="panel-title">系统参数设定</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="system-param-table">
                                    <!-- 系统参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-button" id="send-settings-btn" onclick="handleSendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 确保全局变量可访问
        window.parameterManager = null;

        /**
         * 系统参数页面配置
         * 定义系统参数列表和页面设置
         */

        // 系统参数组 (62个参数)
        const systemParametersGroup = [
            { mqttId: 'SVG_1901', name: '额定电流', unit: 'A' },
            { mqttId: 'SVG_1902', name: '额定电压', unit: 'V' },
            { mqttId: 'SVG_1903', name: '电流定标系数', unit: '' },
            { mqttId: 'SVG_1904', name: '直流侧额定电压', unit: 'V' },
            { mqttId: 'SVG_1905', name: '电网频率', unit: 'Hz' },
            { mqttId: 'SVG_1906', name: '单元载波频率', unit: 'Hz' },
            { mqttId: 'SVG_1907', name: '连接电感', unit: 'mH' },
            { mqttId: 'SVG_1908', name: '直流侧电压指令值', unit: 'V' },
            { mqttId: 'SVG_1909', name: '斜坡速率', unit: '' },
            { mqttId: 'SVG_1910', name: '斜坡输出最小值', unit: '' },
            { mqttId: 'SVG_1911', name: '无功指令电流', unit: 'A' },
            { mqttId: 'SVG_1912', name: '输出电流上限', unit: 'A' },
            { mqttId: 'SVG_1913', name: '无功指令电流斜坡', unit: '' },
            { mqttId: 'SVG_1915', name: '一段电流 CT 变化比', unit: '' },
            { mqttId: 'SVG_1916', name: 'SVG 霍尔电流传感器变比', unit: '' },
            { mqttId: 'SVG_1917', name: '电网二段电流 CT 变比', unit: '' },
            { mqttId: 'SVG_1918', name: 'SVG 保护 CT 变比', unit: '' },
            { mqttId: 'SVG_1919', name: '电网一段电压变化比', unit: '' },
            { mqttId: 'SVG_1920', name: '无功电流下限', unit: 'A' },
            { mqttId: 'SVG_1921', name: '直流侧与无功电流系数', unit: '' },
            { mqttId: 'SVG_1922', name: '无功电流滞后系数', unit: '' },
            { mqttId: 'SVG_1923', name: '死区校正系数', unit: '' },
            { mqttId: 'SVG_1924', name: '感性无功偏移系数', unit: '' },
            { mqttId: 'SVG_1925', name: '感性无功增益', unit: '' },
            { mqttId: 'SVG_1926', name: '容性无功偏移系数', unit: '' },
            { mqttId: 'SVG_1927', name: '容性无功增益', unit: '' },
            { mqttId: 'SVG_1928', name: '低电压穿越保护阀值 1', unit: 'V' },
            { mqttId: 'SVG_1929', name: '低电压穿越保护阀值 2', unit: 'V' },
            { mqttId: 'SVG_1930', name: '电网二段电压变化比', unit: '' },
            { mqttId: 'SVG_1931', name: 'PT 板 RC 移相角度补偿', unit: '°' },
            { mqttId: 'SVG_1932', name: '功率因数设定值', unit: '' },
            { mqttId: 'SVG_1933', name: '上级网侧电流 1 段变比', unit: '' },
            { mqttId: 'SVG_1934', name: '上级网侧电流 2 段变比', unit: '' },
            { mqttId: 'SVG_1935', name: '电压基准', unit: 'V' },
            { mqttId: 'SVG_1936', name: '电网电压指令', unit: 'V' },
            { mqttId: 'SVG_1937', name: '电网电压指令斜坡', unit: '' },
            { mqttId: 'SVG_1938', name: 'SVG 变压器变比', unit: '' },
            { mqttId: 'SVG_1939', name: '电压补偿死区', unit: 'V' },
            { mqttId: 'SVG_1940', name: '单元过压保护跳闸时间', unit: 's' },
            { mqttId: 'SVG_1941', name: '单元过压时间', unit: 's' },
            { mqttId: 'SVG_1942', name: 'PWM 板 1 级数', unit: '' },
            { mqttId: 'SVG_1943', name: 'PWM 板 2 级数', unit: '' },
            { mqttId: 'SVG_1944', name: 'PWM 板 3 级数', unit: '' },
            { mqttId: 'SVG_1945', name: '电压 d 轴无功电流系数', unit: '' },
            { mqttId: 'SVG_1946', name: '电压 q 轴无功电流系数', unit: '' },
            { mqttId: 'SVG_1947', name: 'PWM 板 4 级数', unit: '' },
            { mqttId: 'SVG_1948', name: 'PWM 板 5 级数', unit: '' },
            { mqttId: 'SVG_1949', name: 'PWM 板 6 级数', unit: '' },
            { mqttId: 'SVG_1950', name: '锁相环 Kp', unit: '' },
            { mqttId: 'SVG_1951', name: '锁相环 Ki', unit: '' },
            { mqttId: 'SVG_1952', name: 'SVG 升压变压器移相角度', unit: '°' },
            { mqttId: 'SVG_1953', name: '模拟板移相角', unit: '°' },
            { mqttId: 'SVG_1954', name: '传输延时补偿角度', unit: '°' },
            { mqttId: 'SVG_1955', name: '平衡控制补偿角度', unit: '°' },
            { mqttId: 'SVG_1956', name: '直流电流抑制 Kp', unit: '' },
            { mqttId: 'SVG_1957', name: '直流电流抑制 Ki', unit: '' },
            { mqttId: 'SVG_1958', name: '超前滞后环节 Tb 系数', unit: '' },
            { mqttId: 'SVG_1959', name: '超前滞后环节 Ta 系数', unit: '' },
            { mqttId: 'SVG_1962', name: 'd 轴正向辅助校正系数', unit: '' },
            { mqttId: 'SVG_1963', name: 'q 轴正向辅助校正系数', unit: '' },
            { mqttId: 'SVG_1964', name: 'd 轴负向辅助校正系数', unit: '' },
            { mqttId: 'SVG_1965', name: 'q 轴负向辅助校正系数', unit: '' }
        ];

        // 页面配置对象
        const systemParamConfig = {
            pageTitle: '系统参数',
            panelTitles: ['系统参数设定'],
            parameters: systemParametersGroup,
            parametersPerPanel: [62], // 单个面板包含所有62个参数
            parametersPerRow: 4 // 每行显示4个参数
        };

        /**
         * 系统参数专用参数配置管理类
         * 扩展通用参数配置管理器以支持浮点数输入控件
         */
        class SystemParameterManager extends ParameterConfigManager {
            constructor(config) {
                super(config);
            }

            /**
             * 初始化参数定义（重写以确保正确的序号）
             */
            initializeParams() {
                if (!this.config.parameters || !Array.isArray(this.config.parameters)) {
                    console.error('参数配置无效：parameters 必须是数组');
                    return;
                }

                this.config.parameters.forEach((param, index) => {
                    this.parameters.push({
                        id: `param_${index + 1}`,
                        index: index + 1, // 全局序号
                        mqttId: param.mqttId,
                        name: param.name,
                        unit: param.unit || '',
                        currentValue: 0.00,
                        settingValue: 0.00,
                        panel: 1, // 单面板
                        isInitialized: false
                    });
                });

                console.log(`初始化了 ${this.parameters.length} 个系统参数`);
            }

            /**
             * 创建参数表格界面（重写以支持每行四个参数的布局）
             */
            createParamTables() {
                const tableBody = document.getElementById('system-param-table');

                if (!tableBody) {
                    console.error('未找到系统参数表格容器');
                    return;
                }

                // 更新页面标题
                if (this.config.pageTitle) {
                    const headerTitle = document.querySelector('.header h1');
                    if (headerTitle) {
                        headerTitle.textContent = this.config.pageTitle;
                    }
                }

                // 按每行四个参数创建表格行
                const parametersPerRow = this.config.parametersPerRow || 4;
                for (let i = 0; i < this.parameters.length; i += parametersPerRow) {
                    const rowParams = [];
                    for (let j = 0; j < parametersPerRow && (i + j) < this.parameters.length; j++) {
                        rowParams.push(this.parameters[i + j]);
                    }

                    const row = this.createFourParamRow(rowParams);
                    tableBody.appendChild(row);
                }

                // 初始化所有参数的高亮状态
                this.initializeAllHighlights();
            }

            /**
             * 创建包含四个参数的表格行
             * @param {Array} params - 参数数组（最多4个参数）
             */
            createFourParamRow(params) {
                const row = document.createElement('tr');
                let rowHTML = '';

                // 创建每个参数的HTML
                params.forEach(param => {
                    rowHTML += this.createSingleParamHTML(param);
                });

                // 如果参数不足4个，填充空单元格
                const remainingCells = (4 - params.length) * 4; // 每个参数占4列（去除单位列）
                for (let i = 0; i < remainingCells; i++) {
                    rowHTML += '<td></td>';
                }

                row.innerHTML = rowHTML;
                return row;
            }

            /**
             * 创建单个参数的HTML内容
             * @param {Object} param - 参数对象
             */
            createSingleParamHTML(param) {
                const controlHTML = this.createFloatControl(param);
                // 确保当前值格式化为2位小数
                const currentValueText = (param.currentValue || 0.00).toFixed(2);

                return `
                    <td class="param-index" data-param-id="${param.id}" data-cell-type="index">${param.index}</td>
                    <td class="param-name" data-param-id="${param.id}" data-cell-type="name">${param.name}</td>
                    <td class="param-setting" data-param-id="${param.id}" data-cell-type="setting">
                        ${controlHTML}
                    </td>
                    <td class="param-current" data-param-id="${param.id}" data-cell-type="current" id="current-${param.id}">${currentValueText}</td>
                `;
            }

            /**
             * 创建浮点数输入控件
             * @param {Object} param - 参数对象
             */
            createFloatControl(param) {
                // 确保设定值格式化为2位小数
                const formattedValue = (param.settingValue || 0.00).toFixed(2);

                return `
                    <div class="control-container">
                        <input type="number"
                               class="float-input"
                               id="input-${param.id}"
                               value="${formattedValue}"
                               min="-99999.00"
                               max="99999.00"
                               step="0.01"
                               onchange="systemParamManager.updateFloatParam('${param.id}', this.value)"
                               onblur="systemParamManager.validateFloatParam('${param.id}', this)">
                    </div>
                `;
            }

            /**
             * 更新浮点数参数
             * @param {string} paramId - 参数ID
             * @param {string} value - 新值
             */
            updateFloatParam(paramId, value) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const numValue = parseFloat(value);
                const min = -99999.00;
                const max = 99999.00;

                if (isNaN(numValue)) {
                    // 使用默认值0.00
                    param.settingValue = 0.00;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = param.settingValue.toFixed(2);
                    }
                } else if (numValue < min || numValue > max) {
                    // 超出范围，使用边界值
                    const boundaryValue = numValue < min ? min : max;
                    param.settingValue = boundaryValue;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = param.settingValue.toFixed(2);
                    }

                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为 ${param.settingValue.toFixed(2)}`, 'warning');
                } else {
                    // 有效值，保留完整精度用于计算
                    param.settingValue = numValue;
                    // 确保输入框显示格式化后的值
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = param.settingValue.toFixed(2);
                    }
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue.toFixed(2)}`);
            }

            /**
             * 验证浮点数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateFloatParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const value = parseFloat(inputElement.value);
                const min = -99999.00;
                const max = 99999.00;

                if (isNaN(value) || value < min || value > max) {
                    // 使用当前设定值或默认值
                    const defaultValue = param.settingValue || 0.00;
                    param.settingValue = defaultValue;
                    inputElement.value = param.settingValue.toFixed(2);

                    if (!isNaN(value) && (value < min || value > max)) {
                        showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已恢复为 ${param.settingValue.toFixed(2)}`, 'warning');
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                } else {
                    // 有效值，确保格式化显示
                    param.settingValue = value;
                    inputElement.value = param.settingValue.toFixed(2);
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 更新参数当前值（从 MQTT 数据）
             * 重写以支持浮点数格式化
             */
            updateCurrentValueFromMQTT(mqttId, value) {
                console.log(`[SystemParameterManager] updateCurrentValueFromMQTT 被调用: mqttId=${mqttId}, value=${value}, type=${typeof value}`);

                const param = this.parameters.find(p => p.mqttId === mqttId);
                if (!param) {
                    console.warn(`[SystemParameterManager] 未找到参数: mqttId=${mqttId}`);
                    return;
                }

                // 确保数值解析和格式化正确
                const numericValue = parseFloat(value);
                if (isNaN(numericValue)) {
                    console.warn(`[SystemParameterManager] 参数 ${param.name} 收到无效数值: ${value}`);
                    return;
                }

                // 更新参数当前值，保留完整精度用于计算
                param.currentValue = numericValue;

                // 首次数据同步：如果参数未初始化，将设定值设为当前值
                if (!param.isInitialized) {
                    param.settingValue = param.currentValue;
                    param.isInitialized = true;

                    // 更新输入框界面，确保格式化为2位小数
                    const inputElement = document.getElementById(`input-${param.id}`);
                    if (inputElement) {
                        inputElement.value = param.settingValue.toFixed(2);
                    }

                    // 记录修改
                    this.modifiedValues[param.id] = param.settingValue;

                    console.log(`[SystemParameterManager] 首次同步参数 ${param.name}: 当前值=${param.currentValue.toFixed(2)}, 设定值=${param.settingValue.toFixed(2)} (已初始化)`);
                }

                // 更新界面显示（当前值），确保格式化为2位小数
                const currentElement = document.getElementById(`current-${param.id}`);
                if (currentElement) {
                    const formattedValue = param.currentValue.toFixed(2);
                    currentElement.textContent = formattedValue;
                    currentElement.style.color = '#00d4ff';

                    console.log(`[SystemParameterManager] 更新参数 ${param.name} 显示: 原始值=${value}, 解析值=${numericValue}, 格式化值=${formattedValue}`);
                } else {
                    console.warn(`[SystemParameterManager] 未找到当前值显示元素: current-${param.id}`);
                }

                // 更新高亮状态
                this.updateHighlightStatus(param.id);
            }
        }

        // 全局系统参数管理器变量
        let systemParamManager = null;

        /**
         * 检查参数管理器状态
         * @returns {boolean} 参数管理器是否已正确初始化
         */
        function checkParameterManagerStatus() {
            console.log('检查参数管理器状态...');
            console.log('systemParamManager:', systemParamManager);
            console.log('window.parameterManager:', window.parameterManager);

            if (!systemParamManager) {
                console.error('systemParamManager 未初始化');
                return false;
            }

            if (!window.parameterManager) {
                console.error('window.parameterManager 未设置');
                return false;
            }

            console.log('参数管理器状态检查通过');
            return true;
        }

        /**
         * 处理参数设置发送（系统参数专用）
         */
        async function handleSendParameterSettings() {
            console.log('开始发送系统参数设置...');

            // 检查参数管理器状态
            if (!checkParameterManagerStatus()) {
                showStatusMessage('参数管理器未正确初始化，请刷新页面重试', 'error');
                return;
            }

            // 检查 MQTT 连接状态
            if (!mqttParameterManager) {
                showStatusMessage('MQTT 管理器未初始化', 'error');
                return;
            }

            const connectionStatus = mqttParameterManager.getConnectionStatus();
            if (!connectionStatus.isConnected) {
                showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
                return;
            }

            // 获取需要发送的参数
            const modifiedParams = systemParamManager.getModifiedParameters();

            if (modifiedParams.length === 0) {
                showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
                return;
            }

            // 禁用发送按钮
            const sendButton = document.getElementById('send-settings-btn');
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            try {
                // 获取 MQTT 格式的参数数组
                const mqttParams = systemParamManager.getMQTTParameterArray();

                console.log('准备发送的系统参数:', mqttParams);

                // 发送参数设置
                const result = await mqttParameterManager.sendParameterSettings(mqttParams);

                showStatusMessage(
                    `系统参数设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
                    'success'
                );

                console.log('系统参数设置发送成功:', result);

            } catch (error) {
                console.error('发送系统参数设置失败:', error);
                showStatusMessage(`发送失败: ${error.message}`, 'error');
            } finally {
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '下载';
                }
            }
        }

        /**
         * 初始化系统参数配置页面
         * @param {Object} config - 系统参数配置对象
         */
        function initSystemParameterConfigPage(config) {
            console.log('系统参数配置页面初始化...');

            try {
                // 初始化系统参数管理器
                systemParamManager = new SystemParameterManager(config);

                // 设置全局变量以兼容通用脚本
                window.parameterManager = systemParamManager;

                // 创建备用的全局发送函数
                window.sendParameterSettings = handleSendParameterSettings;

                console.log('系统参数管理器初始化成功:', systemParamManager);
                console.log('全局参数管理器设置成功:', window.parameterManager);

                // 初始化 MQTT 连接
                initMQTTConnection();

                // 定期更新连接状态显示和按钮状态
                setInterval(() => {
                    if (mqttParameterManager) {
                        const status = mqttParameterManager.getConnectionStatus();
                        if (status.isConnected) {
                            updateMQTTStatus('connected', 'MQTT 已连接');
                        } else {
                            updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                        }
                        // 更新发送按钮状态
                        updateSendButtonStatus();
                    }
                }, 1000);

                console.log('系统参数配置页面初始化完成');

            } catch (error) {
                console.error('系统参数配置页面初始化失败:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        }

        /**
         * 测试浮点数显示功能
         */
        function testFloatDisplay() {
            console.log('开始测试浮点数显示功能...');

            if (!systemParamManager) {
                console.error('系统参数管理器未初始化');
                return;
            }

            // 测试数据
            const testData = [
                { id: 'SVG_1912', value: '2.049999952316284' },
                { id: 'SVG_1915', value: '100.0' },
                { id: 'SVG_1901', value: '50.123456789' }
            ];

            testData.forEach(item => {
                console.log(`测试参数 ${item.id}, 值: ${item.value}`);
                systemParamManager.updateCurrentValueFromMQTT(item.id, item.value);
            });
        }

        // 将测试函数暴露到全局作用域
        window.testFloatDisplay = testFloatDisplay;

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 内容加载完成，开始初始化系统参数配置页面...');

            // 检查必要的依赖
            if (typeof ParameterConfigManager === 'undefined') {
                console.error('ParameterConfigManager 类未定义，请检查通用脚本是否正确加载');
                showStatusMessage('页面初始化失败：缺少必要的依赖脚本', 'error');
                return;
            }

            if (typeof systemParamConfig === 'undefined') {
                console.error('systemParamConfig 配置对象未定义');
                showStatusMessage('页面初始化失败：缺少配置对象', 'error');
                return;
            }

            console.log('依赖检查通过，开始初始化系统参数管理器...');
            console.log('系统参数配置:', systemParamConfig);

            // 使用自定义的系统参数配置管理器初始化页面
            try {
                initSystemParameterConfigPage(systemParamConfig);

                // 延迟检查初始化状态
                setTimeout(() => {
                    if (checkParameterManagerStatus()) {
                        console.log('系统参数管理器初始化验证成功');
                        showStatusMessage('页面初始化完成', 'success');
                    } else {
                        console.error('系统参数管理器初始化验证失败');
                        showStatusMessage('参数管理器初始化异常，请刷新页面', 'error');
                    }
                }, 1000);

            } catch (error) {
                console.error('页面初始化过程中发生错误:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>
