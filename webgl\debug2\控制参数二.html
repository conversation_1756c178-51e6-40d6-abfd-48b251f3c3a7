<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制参数二 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 控制参数二专用样式 -->
    <style>
        /* 控制参数二专用样式扩展 */
        .control-param-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0;
            margin-bottom: 15px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            position: relative;
            padding-bottom: 70px;
            overflow: hidden;
            min-height: 0;
        }

        /* 控件容器样式 - 统一所有控件的容器 */
        .control-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 0;
        }

        /* 浮点数输入框样式 */
        .float-input {
            width: 120px;
            height: 32px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .float-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .float-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        /* 单面板布局样式 */
        .main-layout {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 120px);
            max-height: calc(100vh - 120px);
        }

        .single-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
        }

        .control-param-panel {
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 10px;
            overflow: hidden;
            min-width: 0;
            display: flex;
            flex-direction: column;
            flex: 1;
            min-height: 0;
        }

        /* 表格容器样式 */
        .table-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }

        /* 面板标题优化 */
        .panel-title {
            font-size: 16px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 8px;
            padding: 6px 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* 表格样式优化 - 支持每行两个参数 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 8px 4px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.4);
            font-weight: bold;
            font-size: 13px;
        }

        .params-table td {
            padding: 4px 3px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(42, 49, 66, 0.7);
            vertical-align: middle;
            height: 55px;
        }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        /* 序号列样式 */
        .param-index {
            font-size: 12px;
            color: #7a8ba0;
            font-weight: bold;
            width: 40px;
            text-align: center;
        }

        /* 参数名称列样式 */
        .param-name {
            font-size: 13px;
            color: #ffffff;
            text-align: left;
            padding-left: 6px;
            line-height: 1.2;
            max-width: 180px;
            width: 180px;
            word-wrap: break-word;
            overflow: visible;
            white-space: normal;
        }

        /* 当前值列样式 */
        .param-current {
            font-size: 12px;
            color: #00d4ff;
            font-weight: bold;
            width: 100px;
            text-align: center;
        }

        /* 设定值列样式 */
        .param-setting {
            width: 130px;
            text-align: center;
            padding: 0 5px;
        }

        /* 分组标题样式 */
        .group-title {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            font-weight: bold;
            text-align: center;
            font-size: 14px;
        }

        .group-title td {
            padding: 8px;
            border: 2px solid rgba(0, 212, 255, 0.5);
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>控制参数二</h1>
        </div>

        <div class="main-content">
            <div class="main-layout">
                <!-- 单面板：控制参数二 -->
                <div class="single-panel">
                    <div class="control-param-panel">
                        <div class="panel-title">控制参数二设定</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="control-param-table">
                                    <!-- 控制参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-button" id="send-settings-btn" onclick="handleSendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 确保全局变量可访问
        window.parameterManager = null;

        /**
         * 控制参数二页面配置
         * 定义控制参数列表和页面设置
         */

        // 控制参数二组 (40个参数)
        const controlParametersGroup2 = [
            // 相直流侧电压平衡控制 (10个参数)
            { mqttId: 'SVG_1401', name: '比例系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1402', name: 'kc系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1403', name: '积分系数', type: 'float', decimals: 4, min: -99999.0000, max: 99999.0000 },
            { mqttId: 'SVG_1404', name: '微分系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1405', name: '输出上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1406', name: '输出下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1407', name: '积分上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1408', name: '积分下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1409', name: '积分初始值', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1410', name: '备用', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            
            // 单元电压平衡控制 (10个参数)
            { mqttId: 'SVG_1501', name: '比例系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1502', name: 'kc系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1503', name: '积分系数', type: 'float', decimals: 5, min: -99999.00000, max: 99999.00000 },
            { mqttId: 'SVG_1504', name: '微分系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1505', name: '输出上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1506', name: '输出下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1507', name: '积分上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1508', name: '积分下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1509', name: '积分初始值', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_1510', name: '备用', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            
            // 电网电压控制 (10个参数)
            { mqttId: 'SVG_2401', name: '调节比例系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2402', name: '调节Kc系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2403', name: '调节积分系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2404', name: '调节微分系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2405', name: '输出上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2406', name: '输出下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2407', name: '积分上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2408', name: '积分下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2409', name: '积分初始值', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2410', name: '备用1', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            
            // 电网电压无功控制 (10个参数)
            { mqttId: 'SVG_2501', name: '调节比例系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2502', name: '调节Kc系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2503', name: '调节积分系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2504', name: '调节微分系数', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2505', name: '输出上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2506', name: '输出下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2507', name: '积分上限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2508', name: '积分下限', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2509', name: '积分初始值', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 },
            { mqttId: 'SVG_2510', name: '备用1', type: 'float', decimals: 2, min: -99999.00, max: 99999.00 }
        ];

        // 页面配置对象
        const controlParamConfig2 = {
            pageTitle: '控制参数二',
            panelTitles: ['控制参数二设定'],
            parameters: controlParametersGroup2,
            parametersPerPanel: [40], // 单个面板包含所有40个参数
            parametersPerRow: 2 // 每行显示2个参数
        };

        /**
         * 控制参数二专用参数配置管理类
         * 扩展通用参数配置管理器以支持浮点数输入控件
         */
        class ControlParameterManager2 extends ParameterConfigManager {
            constructor(config) {
                super(config);
            }

            /**
             * 初始化参数定义（重写以确保正确的序号）
             */
            initializeParams() {
                if (!this.config.parameters || !Array.isArray(this.config.parameters)) {
                    console.error('参数配置无效：parameters 必须是数组');
                    return;
                }

                this.config.parameters.forEach((param, index) => {
                    this.parameters.push({
                        id: `param_${index + 1}`,
                        index: index + 1, // 全局序号
                        mqttId: param.mqttId,
                        name: param.name,
                        type: param.type || 'float',
                        decimals: param.decimals || 2,
                        min: param.min || -99999,
                        max: param.max || 99999,
                        currentValue: 0.00,
                        settingValue: 0.00,
                        panel: 1, // 单面板
                        isInitialized: false
                    });
                });

                console.log(`初始化了 ${this.parameters.length} 个控制参数二`);
            }

            /**
             * 创建参数表格界面（重写以支持每行两个参数的布局）
             */
            createParamTables() {
                const tableBody = document.getElementById('control-param-table');

                if (!tableBody) {
                    console.error('未找到控制参数表格容器');
                    return;
                }

                // 更新页面标题
                if (this.config.pageTitle) {
                    const headerTitle = document.querySelector('.header h1');
                    if (headerTitle) {
                        headerTitle.textContent = this.config.pageTitle;
                    }
                }

                // 添加分组标题和参数行
                this.addGroupWithParams(tableBody, '相直流侧电压平衡控制', 0, 10);
                this.addGroupWithParams(tableBody, '单元电压平衡控制', 10, 10);
                this.addGroupWithParams(tableBody, '电网电压控制', 20, 10);
                this.addGroupWithParams(tableBody, '电网电压无功控制', 30, 10);

                // 初始化所有参数的高亮状态
                this.initializeAllHighlights();
            }

            /**
             * 添加参数组及其参数行
             * @param {HTMLElement} tableBody - 表格主体元素
             * @param {string} groupTitle - 组标题
             * @param {number} startIndex - 起始参数索引
             * @param {number} count - 参数数量
             */
            addGroupWithParams(tableBody, groupTitle, startIndex, count) {
                // 添加分组标题行
                const groupRow = document.createElement('tr');
                groupRow.className = 'group-title';
                groupRow.innerHTML = `<td colspan="8">${groupTitle}</td>`;
                tableBody.appendChild(groupRow);

                // 按每行两个参数创建表格行
                const parametersPerRow = this.config.parametersPerRow || 2;
                for (let i = startIndex; i < startIndex + count; i += parametersPerRow) {
                    const rowParams = [];
                    for (let j = 0; j < parametersPerRow && (i + j) < startIndex + count && (i + j) < this.parameters.length; j++) {
                        rowParams.push(this.parameters[i + j]);
                    }

                    const row = this.createTwoParamRow(rowParams);
                    tableBody.appendChild(row);
                }
            }

            /**
             * 创建包含两个参数的表格行
             * @param {Array} params - 参数数组（最多2个参数）
             */
            createTwoParamRow(params) {
                const row = document.createElement('tr');
                let rowHTML = '';

                // 创建每个参数的HTML
                params.forEach(param => {
                    rowHTML += this.createSingleParamHTML(param);
                });

                // 如果参数不足2个，填充空单元格
                const remainingCells = (2 - params.length) * 4; // 每个参数占4列
                for (let i = 0; i < remainingCells; i++) {
                    rowHTML += '<td></td>';
                }

                row.innerHTML = rowHTML;
                return row;
            }

            /**
             * 创建单个参数的HTML内容
             * @param {Object} param - 参数对象
             */
            createSingleParamHTML(param) {
                const controlHTML = this.createFloatInputControl(param);
                const currentValueText = parseFloat(param.currentValue).toFixed(param.decimals);

                return `
                    <td class="param-index" data-param-id="${param.id}" data-cell-type="index">${param.index}</td>
                    <td class="param-name" data-param-id="${param.id}" data-cell-type="name">${param.name}</td>
                    <td class="param-setting" data-param-id="${param.id}" data-cell-type="setting">
                        ${controlHTML}
                    </td>
                    <td class="param-current" data-param-id="${param.id}" data-cell-type="current" id="current-${param.id}">${currentValueText}</td>
                `;
            }

            /**
             * 创建浮点数输入控件
             * @param {Object} param - 参数对象
             */
            createFloatInputControl(param) {
                const step = param.decimals === 5 ? '0.00001' :
                            param.decimals === 4 ? '0.0001' :
                            param.decimals === 3 ? '0.001' : '0.01';
                return `
                    <div class="control-container">
                        <input type="number"
                               class="float-input"
                               id="input-${param.id}"
                               value="${parseFloat(param.settingValue).toFixed(param.decimals)}"
                               min="${param.min}"
                               max="${param.max}"
                               step="${step}"
                               onchange="controlParamManager2.updateFloatParam('${param.id}', this.value)"
                               onblur="controlParamManager2.validateFloatParam('${param.id}', this)">
                    </div>
                `;
            }

            /**
             * 更新浮点数参数
             * @param {string} paramId - 参数ID
             * @param {string} value - 新值
             */
            updateFloatParam(paramId, value) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const numValue = parseFloat(value);
                const min = param.min;
                const max = param.max;

                if (isNaN(numValue)) {
                    // 使用默认值0.00
                    param.settingValue = 0.00;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = '0.00';
                    }
                } else if (numValue < min || numValue > max) {
                    // 超出范围，使用边界值
                    const boundaryValue = numValue < min ? min : max;
                    param.settingValue = boundaryValue;
                    const inputElement = document.getElementById(`input-${paramId}`);
                    if (inputElement) {
                        inputElement.value = boundaryValue.toFixed(param.decimals);
                    }

                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为 ${boundaryValue.toFixed(param.decimals)}`, 'warning');
                } else {
                    // 有效值
                    param.settingValue = parseFloat(numValue.toFixed(param.decimals));
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 验证浮点数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateFloatParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const value = parseFloat(inputElement.value);
                const min = param.min;
                const max = param.max;

                if (isNaN(value) || value < min || value > max) {
                    // 使用当前设定值或默认值
                    const defaultValue = param.settingValue || 0.00;
                    param.settingValue = defaultValue;
                    inputElement.value = defaultValue.toFixed(param.decimals);

                    if (!isNaN(value) && (value < min || value > max)) {
                        showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已恢复为 ${defaultValue.toFixed(param.decimals)}`, 'warning');
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 更新参数当前值（从 MQTT 数据）
             * 重写以支持浮点数格式化
             */
            updateCurrentValueFromMQTT(mqttId, value) {
                const param = this.parameters.find(p => p.mqttId === mqttId);
                if (!param) return;

                // 更新参数当前值
                param.currentValue = parseFloat(value) || 0.00;

                // 首次数据同步：如果参数未初始化，将设定值设为当前值
                if (!param.isInitialized) {
                    param.settingValue = param.currentValue;
                    param.isInitialized = true;

                    // 更新输入框界面
                    const inputElement = document.getElementById(`input-${param.id}`);
                    if (inputElement) {
                        inputElement.value = parseFloat(param.settingValue).toFixed(param.decimals);
                    }

                    // 记录修改
                    this.modifiedValues[param.id] = param.settingValue;

                    console.log(`首次同步参数 ${param.name}: 当前值=${param.currentValue.toFixed(param.decimals)}, 设定值=${param.settingValue.toFixed(param.decimals)} (已初始化)`);
                }

                // 更新界面显示（当前值）
                const currentElement = document.getElementById(`current-${param.id}`);
                if (currentElement) {
                    currentElement.textContent = param.currentValue.toFixed(param.decimals);
                    currentElement.style.color = '#00d4ff';
                }

                // 更新高亮状态
                this.updateHighlightStatus(param.id);
            }
        }

        // 全局控制参数管理器变量
        let controlParamManager2 = null;

        /**
         * 检查参数管理器状态
         * @returns {boolean} 参数管理器是否已正确初始化
         */
        function checkParameterManagerStatus() {
            console.log('检查参数管理器状态...');
            console.log('controlParamManager2:', controlParamManager2);
            console.log('window.parameterManager:', window.parameterManager);

            if (!controlParamManager2) {
                console.error('controlParamManager2 未初始化');
                return false;
            }

            if (!window.parameterManager) {
                console.error('window.parameterManager 未设置');
                return false;
            }

            console.log('参数管理器状态检查通过');
            return true;
        }

        /**
         * 处理参数设置发送（控制参数二专用）
         */
        async function handleSendParameterSettings() {
            console.log('开始发送控制参数二设置...');

            // 检查参数管理器状态
            if (!checkParameterManagerStatus()) {
                showStatusMessage('参数管理器未正确初始化，请刷新页面重试', 'error');
                return;
            }

            // 检查 MQTT 连接状态
            if (!mqttParameterManager) {
                showStatusMessage('MQTT 管理器未初始化', 'error');
                return;
            }

            const connectionStatus = mqttParameterManager.getConnectionStatus();
            if (!connectionStatus.isConnected) {
                showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
                return;
            }

            // 获取需要发送的参数
            const modifiedParams = controlParamManager2.getModifiedParameters();

            if (modifiedParams.length === 0) {
                showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
                return;
            }

            // 禁用发送按钮
            const sendButton = document.getElementById('send-settings-btn');
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            try {
                // 获取 MQTT 格式的参数数组
                const mqttParams = controlParamManager2.getMQTTParameterArray();

                console.log('准备发送的控制参数二:', mqttParams);

                // 发送参数设置
                const result = await mqttParameterManager.sendParameterSettings(mqttParams);

                showStatusMessage(
                    `控制参数二设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
                    'success'
                );

                console.log('控制参数二设置发送成功:', result);

            } catch (error) {
                console.error('发送控制参数二设置失败:', error);
                showStatusMessage(`发送失败: ${error.message}`, 'error');
            } finally {
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '下载';
                }
            }
        }

        /**
         * 初始化控制参数二配置页面
         * @param {Object} config - 控制参数二配置对象
         */
        function initControlParameterConfigPage2(config) {
            console.log('控制参数二配置页面初始化...');

            try {
                // 初始化控制参数二管理器
                controlParamManager2 = new ControlParameterManager2(config);

                // 设置全局变量以兼容通用脚本
                window.parameterManager = controlParamManager2;

                // 创建备用的全局发送函数
                window.sendParameterSettings = handleSendParameterSettings;

                console.log('控制参数二管理器初始化成功:', controlParamManager2);
                console.log('全局参数管理器设置成功:', window.parameterManager);

                // 初始化 MQTT 连接
                initMQTTConnection();

                // 定期更新连接状态显示和按钮状态
                setInterval(() => {
                    if (mqttParameterManager) {
                        const status = mqttParameterManager.getConnectionStatus();
                        if (status.isConnected) {
                            updateMQTTStatus('connected', 'MQTT 已连接');
                        } else {
                            updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                        }
                        // 更新发送按钮状态
                        updateSendButtonStatus();
                    }
                }, 1000);

                console.log('控制参数二配置页面初始化完成');

            } catch (error) {
                console.error('控制参数二配置页面初始化失败:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        }

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 内容加载完成，开始初始化控制参数二配置页面...');

            // 检查必要的依赖
            if (typeof ParameterConfigManager === 'undefined') {
                console.error('ParameterConfigManager 类未定义，请检查通用脚本是否正确加载');
                showStatusMessage('页面初始化失败：缺少必要的依赖脚本', 'error');
                return;
            }

            if (typeof controlParamConfig2 === 'undefined') {
                console.error('controlParamConfig2 配置对象未定义');
                showStatusMessage('页面初始化失败：缺少配置对象', 'error');
                return;
            }

            console.log('依赖检查通过，开始初始化控制参数二管理器...');
            console.log('控制参数二配置:', controlParamConfig2);

            // 使用自定义的控制参数二配置管理器初始化页面
            try {
                initControlParameterConfigPage2(controlParamConfig2);

                // 延迟检查初始化状态
                setTimeout(() => {
                    if (checkParameterManagerStatus()) {
                        console.log('控制参数二管理器初始化验证成功');
                        showStatusMessage('页面初始化完成', 'success');
                    } else {
                        console.error('控制参数二管理器初始化验证失败');
                        showStatusMessage('参数管理器初始化异常，请刷新页面', 'error');
                    }
                }, 1000);

            } catch (error) {
                console.error('页面初始化过程中发生错误:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>