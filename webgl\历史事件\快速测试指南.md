# 历史事件页面快速测试指南

## 当前状态

✅ **页面功能已完成**：
- API接口集成（带认证）
- 表格结构调整
- 分页功能
- 筛选功能
- 错误处理

⚠️ **CORS问题**：
- 跨域请求被浏览器阻止
- 需要服务器端配置解决

## 立即测试方案

### 方案1: 使用模拟数据模式（推荐）

1. **打开页面**
   - 在浏览器中打开 `webgl/历史事件.html`

2. **遇到CORS错误时**
   - 页面会显示CORS错误提示
   - 点击 **"使用模拟数据"** 按钮

3. **验证功能**
   - ✅ 查看事件列表显示
   - ✅ 测试事件类型筛选（所有事件/报警事件/故障事件）
   - ✅ 测试时间范围筛选
   - ✅ 测试分页功能
   - ✅ 点击事件查看详情

4. **确认显示效果**
   - 表格只有3列：序号、时间、报警事件
   - 时间格式：YYYY-MM-DD HH:mm:ss
   - 右上角显示"模拟数据模式"标识

### 方案2: 解决CORS问题后测试真实API

1. **联系后端开发者**
   - 提供CORS配置要求（见CORS解决方案.md）
   - 确认服务器允许当前域名跨域访问

2. **验证CORS配置**
   - 刷新页面
   - 点击"检测CORS状态"按钮
   - 查看检测结果

3. **测试真实API**
   - 如果使用了模拟数据，在控制台执行：`disableMockDataMode()`
   - 或刷新页面让其自动尝试真实API

## 测试检查清单

### ✅ 基础功能测试
- [ ] 页面正常加载
- [ ] 表格显示3列（序号、时间、报警事件）
- [ ] 时间格式正确（YYYY-MM-DD HH:mm:ss）
- [ ] 事件数据正常显示

### ✅ 筛选功能测试
- [ ] "所有事件"筛选正常
- [ ] "报警事件"筛选正常
- [ ] "故障事件"筛选正常
- [ ] 时间范围筛选正常
- [ ] 筛选后数据更新正确

### ✅ 分页功能测试
- [ ] 分页控件显示正确
- [ ] "上一页"/"下一页"按钮正常
- [ ] 页码信息显示正确
- [ ] 记录数统计正确

### ✅ 交互功能测试
- [ ] 点击事件行显示详情
- [ ] 统计数字更新正确
- [ ] 加载状态显示正常
- [ ] 错误提示友好

### ✅ 认证功能测试
- [ ] 请求头包含Authorization
- [ ] Token格式验证通过
- [ ] 认证错误处理正确

## 开发者调试

### 控制台命令
```javascript
// 启用模拟数据模式
enableMockDataMode()

// 切换到真实API模式
disableMockDataMode()

// 检查当前配置
console.log('API配置:', API_CONFIG)
console.log('开发配置:', DEV_CONFIG)
console.log('当前筛选:', currentFilter)

// 手动触发数据加载
loadHistoryEvents()
```

### 浏览器开发者工具检查
1. **Console标签页**
   - 查看初始化日志
   - 查看API请求日志
   - 查看错误信息

2. **Network标签页**
   - 检查API请求状态
   - 验证请求头信息
   - 查看响应数据

3. **Elements标签页**
   - 检查DOM结构
   - 验证CSS样式
   - 查看动态内容

## 常见问题解决

### Q1: 页面显示空白
**解决方案：**
- 检查浏览器控制台错误
- 确认文件路径正确
- 检查CSS和JS是否加载

### Q2: CORS错误
**解决方案：**
- 点击"使用模拟数据"按钮
- 或联系后端配置CORS
- 或使用代理服务器

### Q3: 认证失败
**解决方案：**
- 检查token是否过期
- 确认token格式正确
- 联系管理员更新token

### Q4: 数据不显示
**解决方案：**
- 检查API响应格式
- 验证数据处理逻辑
- 查看控制台错误信息

## 性能测试

### 加载性能
- [ ] 页面初始加载时间 < 3秒
- [ ] API请求响应时间 < 5秒
- [ ] 分页切换响应时间 < 2秒

### 数据处理
- [ ] 50条数据渲染时间 < 1秒
- [ ] 筛选操作响应时间 < 1秒
- [ ] 内存使用稳定

## 部署建议

### 生产环境
1. 关闭模拟数据模式
2. 确认CORS配置正确
3. 验证认证token有效
4. 测试所有功能正常

### 监控指标
- API请求成功率
- 页面加载时间
- 用户操作响应时间
- 错误发生频率

## 联系支持

如遇到问题，请提供：
1. 浏览器类型和版本
2. 控制台错误信息
3. 网络请求详情
4. 具体操作步骤

**技术支持：**
- 前端问题：检查页面代码和配置
- 后端问题：联系API开发者
- 部署问题：联系运维人员
