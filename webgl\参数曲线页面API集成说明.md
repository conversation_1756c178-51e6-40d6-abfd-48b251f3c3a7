# 参数曲线页面API集成实现说明

## 概述

已成功为 `webgl/参数曲线.html` 页面集成了三个相互关联的API接口，实现了完整的参数曲线监控功能。

## API集成方案

### 1. 第一个接口：设备列表获取（曲线类型选择）

**接口信息：**
- **URL**: `https://exdraw.qizhiyun.cc/prod-api/iot/device/shortList?pageNum=1&pageSize=9999&groupId=8`
- **方法**: GET
- **认证**: Bearer Token（与main.html保持一致）

**功能实现：**
- 页面加载时自动调用 `fetchDeviceList()` 函数
- 将返回数据中的 `deviceName` 字段填充到下拉框中
- 存储每个设备的 `deviceId` 和 `serialNumber` 用于后续接口调用
- 实现了加载状态指示和错误处理

### 2. 第二个接口：设备参数模型获取（参数控制列表）

**接口信息：**
- **URL**: `https://exdraw.qizhiyun.cc/prod-api/iot/device/listThingsModel?deviceId={deviceId}&pageNum=1&pageSize=9999`
- **方法**: GET
- **触发条件**: 用户在设备下拉框中选择设备后立即调用

**功能实现：**
- 动态构建URL，使用选中设备的 `deviceId`
- 调用 `fetchParameterModels(deviceId)` 函数
- 将返回数据中的 `modelName` 字段显示在参数控制列表中
- 为每个参数添加使能开关（toggle switch）
- 默认开启前3个参数的使能开关
- 存储每个参数的完整信息用于第三个接口

### 3. 第三个接口：历史数据获取（图表绘制）

**接口信息：**
- **URL**: `https://exdraw.qizhiyun.cc/prod-api/data/center/deviceHistory`
- **方法**: POST
- **触发条件**: 当任何参数的使能开关处于开启状态时调用

**请求参数构建：**
```javascript
{
  "deviceId": currentDevice.deviceId,
  "serialNumber": currentDevice.serialNumber,
  "identifierList": [
    {
      "identifier": "HMI_31223_0",
      "type": 1
    }
  ],
  "beginTime": "2025-07-25 08:38:48",
  "endTime": "2025-07-25 10:38:48"
}
```

**功能实现：**
- 调用 `fetchHistoricalData()` 函数
- 动态构建请求参数，包含所有开启状态的参数标识符
- 时间范围默认为当前时间1天之内的数据
- 使用ECharts组件将返回的历史数据绘制成参数监控曲线图

## 交互逻辑实现

### 1. 级联选择流程
1. **设备选择** → 触发 `fetchParameterModels()`
2. **参数列表更新** → 显示该设备的所有可监控参数
3. **图表数据更新** → 根据启用的参数获取历史数据并绘制图表

### 2. 实时控制
- 参数使能开关状态变化时立即调用 `handleParameterToggle()`
- 重新构建请求参数并调用 `fetchHistoricalData()`
- 实时更新图表显示

### 3. 默认状态
- 页面加载时自动获取设备列表
- 选择设备后默认开启前3个参数的监控
- 立即获取并显示历史数据

### 4. 错误处理
- 每个API调用都包含完整的错误处理
- 网络错误、认证失败、数据格式错误等都有相应提示
- CORS错误有专门的处理和用户提示

### 5. 加载状态
- 设备列表加载时显示"正在加载设备列表..."
- 参数列表加载时显示"正在加载参数列表..."
- 历史数据加载时图表显示加载动画

## 技术实现细节

### 1. 认证方式
```javascript
const API_CONFIG = {
    baseUrl: 'https://exdraw.qizhiyun.cc/prod-api',
    headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9...'
    }
};
```

### 2. 数据处理
- **设备数据**: 提取 `deviceName`、`deviceId`、`serialNumber`
- **参数数据**: 提取 `modelName`、`identifier`、`type`
- **历史数据**: 解析时间戳和参数值，处理null值

### 3. 图表组件
- 使用ECharts绘制参数监控曲线
- 支持多参数同时显示
- 自动分配颜色，支持null值处理
- 响应式设计，自动调整大小

### 4. UI组件
- 动态生成参数控制项
- 自定义开关样式
- 加载状态指示器
- 错误提示机制

## 核心函数说明

### API调用函数
- `fetchDeviceList()`: 获取设备列表
- `fetchParameterModels(deviceId)`: 获取设备参数模型
- `fetchHistoricalData()`: 获取历史数据

### UI更新函数
- `populateDeviceSelect(devices)`: 填充设备选择下拉框
- `populateParameterControls(parameters)`: 填充参数控制列表
- `createParameterControlItem(parameter, index)`: 创建参数控制项

### 数据处理函数
- `processHistoricalData(data)`: 处理历史数据
- `buildHistoryRequestBody()`: 构建历史数据请求参数
- `updateChart()`: 更新图表显示

### 事件处理函数
- `initializeDeviceSelectEvents()`: 初始化设备选择事件
- `handleParameterToggle(identifier, isEnabled)`: 处理参数开关切换
- `switchTimeMode(mode)`: 切换时间模式

## 样式改进

### 1. 新增CSS样式
- `.loading-indicator`: 加载指示器样式
- `.no-parameters`, `.no-device`: 空状态提示样式
- `.parameter-color`: 参数颜色指示器
- `.parameter-details`: 参数详情布局
- `.toggle-switch`: 自定义开关样式

### 2. 响应式设计
- 图表自动调整大小
- 参数列表滚动支持
- 移动端友好的触摸交互

## 性能优化

### 1. 数据缓存
- 设备列表缓存，避免重复请求
- 参数模型缓存，切换设备时复用数据

### 2. 请求控制
- 合理控制API调用频率
- 避免重复请求相同数据
- 智能的参数变化检测

### 3. 图表优化
- 数据量控制，避免过多数据点
- 平滑动画和过渡效果
- 内存管理和资源释放

## 调试支持

### 1. 控制台日志
- 详细的API请求和响应日志
- 数据处理过程跟踪
- 错误信息记录

### 2. 状态监控
- 实时显示加载状态
- 参数启用状态跟踪
- 图表更新状态监控

## 使用说明

### 1. 基本操作流程
1. 页面加载后自动获取设备列表
2. 在"曲线类型"下拉框中选择设备
3. 系统自动加载该设备的参数列表
4. 默认开启前3个参数，可手动调整开关状态
5. 图表实时显示选中参数的历史数据曲线

### 2. 高级功能
- 支持多参数同时监控
- 实时模式和历史模式切换
- 图表缩放和数据点查看
- 参数颜色区分和图例显示

### 3. 错误处理
- 网络连接问题会显示相应提示
- 认证失败时提供解决建议
- 数据格式错误时自动降级处理

## 注意事项

1. **Token更新**: 需要定期更新Authorization Bearer token
2. **CORS配置**: 需要服务器端配置允许跨域访问
3. **数据格式**: 依赖API返回数据的特定格式
4. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验

## 技术特点

1. **完全集成**: 三个API接口无缝协作
2. **用户友好**: 直观的交互界面和状态提示
3. **实时响应**: 参数变化立即反映在图表中
4. **错误恢复**: 完善的错误处理和恢复机制
5. **性能优化**: 合理的数据管理和渲染优化

改造完成后，参数曲线页面已完全集成真实API，提供了专业级的参数监控功能。
