/**
 * 桂林智源 SVG 数字化系统 - 配置文件
 * 统一管理API认证token和其他全局配置
 */

/**
 * 从Cookies中获取认证Token
 * @returns {string|null} 认证Token或null
 */
function getTokenFromCookies() {
    console.log('[Token获取] 开始从Cookies获取认证Token...');
    
    // 检查是否在浏览器环境中
    if (typeof document === 'undefined') {
        console.warn('[Token获取] 非浏览器环境，无法访问document对象');
        return null;
    }

    console.log('[Token获取] 当前document.cookie内容:', document.cookie);
    
    // 从Cookies中获取Admin-Token
    const cookies = document.cookie.split(';');
    console.log('[Token获取] 解析到的cookie总数:', cookies.length);
    
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        console.log(`[Token获取] 检查第${i + 1}个cookie: "${cookie}"`);
        
        const [name, value] = cookie.split('=');
        console.log(`[Token获取] cookie名称: "${name}", 值长度: ${value ? value.length : 0}`);
        
        if (name === 'Admin-Token') {
            const decodedValue = decodeURIComponent(value);
            console.log('[Token获取] ✅ 成功找到Admin-Token:', decodedValue.substring(0, 20) + '...');
            return decodedValue;
        }
    }
    
    console.warn('[Token获取] ❌ 未找到Admin-Token，所有cookie名称:', 
        cookies.map(c => c.trim().split('=')[0]));
    return null;
}

// 统一的JWT Token配置
const CONFIG = {
    // API基础URL
    BASE_URL: 'https://exdraw.qizhiyun.cc/prod-api',

    // 请求超时时间（毫秒）
    REQUEST_TIMEOUT: 10000,

    // 重试次数
    MAX_RETRIES: 3,

    // 备用Token（当无法从Cookies获取时使用）
    FALLBACK_TOKEN: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjU4Y2FiMGZjLWZhMDUtNDBkMS04N2NhLTlkZmJmZDIzNWMwMyJ9.LAI1LLCz7Cg5A6PvpDE6qNpUHX3ljkGrtLWigObRkOAFN0_OtG888sHbLFEpFe7QjzFCiqp4ttOIfhe9rEU24A',

    // 动态获取JWT认证Token的getter
    get AUTH_TOKEN() {
        const cookieToken = getTokenFromCookies();
        if (cookieToken) {
            return cookieToken;
        }
        // 如果无法从Cookies获取，使用备用Token
        console.warn('无法从Cookies获取认证Token，使用备用Token');
        return this.FALLBACK_TOKEN;
    }
};

/**
 * 获取认证头信息
 * @returns {Object} 包含Authorization头的对象
 */
function getAuthHeader() {
    return {
        'Authorization': `Bearer ${CONFIG.AUTH_TOKEN}`
    };
}

/**
 * 获取完整的请求配置
 * @param {Object} additionalHeaders 额外的请求头
 * @returns {Object} 完整的请求配置
 */
function getRequestConfig(additionalHeaders = {}) {
    return {
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...getAuthHeader(),
            ...additionalHeaders
        },
        timeout: CONFIG.REQUEST_TIMEOUT
    };
}

/**
 * 更新认证Token到Cookies
 * @param {string} newToken 新的JWT Token
 */
function updateAuthToken(newToken) {
    if (typeof document !== 'undefined') {
        // 将新Token写入Cookies
        document.cookie = `Admin-Token=${encodeURIComponent(newToken)}; path=/; domain=.qizhiyun.cc`;
        console.log('认证Token已更新到Cookies');
    } else {
        // 非浏览器环境，更新备用Token
        CONFIG.FALLBACK_TOKEN = newToken;
        console.log('认证Token已更新到备用Token');
    }
}

/**
 * 获取当前Token（调试用）
 * @returns {string} 当前Token
 */
function getCurrentToken() {
    return CONFIG.AUTH_TOKEN;
}

/**
 * 强制刷新Token（从Cookies重新获取）
 * @returns {string} 刷新后的Token
 */
function refreshToken() {
    const token = CONFIG.AUTH_TOKEN; // 触发getter重新获取
    console.log('Token已刷新:', token ? '已获取' : '未获取到');
    return token;
}

// 全局导出，供其他文件使用
if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
    window.getAuthHeader = getAuthHeader;
    window.getRequestConfig = getRequestConfig;
    window.updateAuthToken = updateAuthToken;
    window.getCurrentToken = getCurrentToken;
    window.refreshToken = refreshToken;
    window.getTokenFromCookies = getTokenFromCookies;
}

// ES6模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        getAuthHeader,
        getRequestConfig,
        updateAuthToken,
        getCurrentToken,
        refreshToken,
        getTokenFromCookies
    };
}