<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 版本信息</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 版本信息页面专用样式
         * 基于项目统一的科技蓝色主题设计
         */
        .version-info-container {
            width: 1366px;
            min-height: 768px;
            height: auto;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);

            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
            padding: 20px;
            box-sizing: border-box;
        }

        /* 科技感背景动画 */
        .version-info-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 主内容区域 */
        .version-main {
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: 20px;

        }

        /* 设备信息表格 */
        .device-info-section {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);

            overflow: hidden;
            flex: 0 0 auto;
        }

        .section-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            font-weight: 600;
        }

        .section-header i {
            font-size: 20px;
            color: var(--primary-color);
        }

        /* 设备信息表格样式 */
        .device-info-table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(42, 49, 66, 0.8);
            border: 1px solid var(--border-color);
        }

        .device-info-table th,
        .device-info-table td {
            padding: 12px 20px;
            text-align: center;
            border: 1px solid var(--border-color);
            font-size: 14px;
        }

        .device-info-table th {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.25) 0%,
                rgba(0, 153, 204, 0.25) 100%);
            color: var(--text-primary);
            font-weight: 600;
            border-bottom: 2px solid var(--primary-color);
        }

        .device-info-table td {
            color: var(--text-secondary);
            transition: all 0.3s ease;
            background: rgba(26, 31, 46, 0.6);
        }

        .device-info-table tr:hover td {
            background: rgba(0, 212, 255, 0.08);
            color: var(--text-primary);
        }

        .device-info-table td:first-child {
            color: var(--text-primary);
            font-weight: 500;
            text-align: left;
            background: rgba(0, 212, 255, 0.12);
        }

        /* 版本信息表格区域 */
        .version-tables-section {
            flex: 1;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow-y: auto; /* 允许垂直滚动 */
            display: flex;
            flex-direction: column;
        }

        .version-tables-content {

            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;

        }

        /* 单个版本表格 */
        .version-table-wrapper {
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid var(--border-color);
            border-radius: 8px;

            display: flex;
            flex-direction: column;
        }

        .version-table-header {
            background: linear-gradient(135deg,
                rgba(0, 102, 204, 0.8) 0%,
                rgba(0, 76, 153, 0.8) 100%);
            padding: 12px 15px;
            text-align: center;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
            border-bottom: 2px solid var(--primary-color);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .version-table {
            width: 100%;
            border-collapse: collapse;
            flex: 1;
        }

        .version-table th,
        .version-table td {
            padding: 8px 12px;
            text-align: center;
            border: 1px solid var(--border-color);
            font-size: 12px;
        }

        .version-table th {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.25) 0%,
                rgba(0, 153, 204, 0.25) 100%);
            color: var(--text-primary);
            font-weight: 600;
            border-bottom: 2px solid var(--primary-color);
        }

        .version-table td {
            color: var(--text-secondary);
            transition: all 0.3s ease;
            background: rgba(26, 31, 46, 0.6);
        }

        .version-table tr:hover td {
            background: rgba(0, 212, 255, 0.08);
            color: var(--text-primary);
        }

        .version-table td:first-child {
            text-align: left;
            color: var(--text-primary);
            font-weight: 500;
            background: rgba(0, 212, 255, 0.12);
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .device-info-section,
        .version-tables-section {
            animation: fadeInUp 0.6s ease-out;
        }

        .version-table-wrapper:nth-child(1) {
            animation: fadeInUp 0.8s ease-out;
        }

        .version-table-wrapper:nth-child(2) {
            animation: fadeInUp 1.0s ease-out;
        }

        .version-table-wrapper:nth-child(3) {
            animation: fadeInUp 1.2s ease-out;
        }

        /* 响应式优化 */
        @media (max-width: 1400px) {
            .version-info-container {
                width: 100%;
                max-width: 1366px;
            }
        }

        @media (max-width: 1200px) {
            .version-tables-content {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="version-info-container">
        <main class="version-main">
            <!-- 设备信息表格 -->
            <section class="device-info-section">
                <div class="section-header">
                    <i class="fas fa-info-circle"></i>
                    <h3>设备信息</h3>
                </div>
                <table class="device-info-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>版本</th>
                            <th>修改日期</th>
                        </tr>
                    </thead>
                    <tbody id="deviceInfoBody">
                        <!-- 设备信息将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </section>

            <!-- 版本信息表格区域 -->
            <section class="version-tables-section">
                <div class="section-header">
                    <i class="fas fa-code-branch"></i>
                    <h3>单元版本信息</h3>
                </div>
                <div class="version-tables-content">
                    <!-- A相版本信息 -->
                    <div class="version-table-wrapper">
                        <div class="version-table-header">A相单元版本</div>
                        <table class="version-table">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>版本日期</th>
                                </tr>
                            </thead>
                            <tbody id="phaseAVersions">
                                <!-- A相版本信息将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- B相版本信息 -->
                    <div class="version-table-wrapper">
                        <div class="version-table-header">B相单元版本</div>
                        <table class="version-table">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>版本日期</th>
                                </tr>
                            </thead>
                            <tbody id="phaseBVersions">
                                <!-- B相版本信息将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- C相版本信息 -->
                    <div class="version-table-wrapper">
                        <div class="version-table-header">C相单元版本</div>
                        <table class="version-table">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>版本日期</th>
                                </tr>
                            </thead>
                            <tbody id="phaseCVersions">
                                <!-- C相版本信息将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 版本信息页面脚本
         * 处理版本信息数据展示和初始化功能
         */

        // 设备信息数据
        const deviceInfo = [
            { name: '主控CPU板DSP版本号', version: 'V2.1.3', date: '2024-12-15' },
            { name: '主控CPU板CPLD版本号', version: 'V1.8.2', date: '2024-11-28' },
            { name: '主控A相PWM板FPGA版本号', version: 'V3.2.1', date: '2024-12-10' },
            { name: '主控B相PWM板FPGA版本号', version: 'V3.2.1', date: '2024-12-10' },
            { name: '主控C相PWM板FPGA版本号', version: 'V3.2.1', date: '2024-12-10' }
        ];

        // A相单元版本信息
        const phaseAVersions = [
            { name: 'A相第1单元版本', date: '2024-12-01' },
            { name: 'A相第2单元版本', date: '2024-12-02' },
            { name: 'A相第3单元版本', date: '2024-12-03' },
            { name: 'A相第4单元版本', date: '2024-12-04' },
            { name: 'A相第5单元版本', date: '2024-12-05' },
            { name: 'A相第6单元版本', date: '2024-12-06' },
            { name: 'A相第7单元版本', date: '2024-12-07' },
            { name: 'A相第8单元版本', date: '2024-12-08' },
            { name: 'A相第9单元版本', date: '2024-12-09' },
            { name: 'A相第10单元版本', date: '2024-12-10' },
            { name: 'A相第11单元版本', date: '2024-12-11' },
            { name: 'A相第12单元版本', date: '2024-12-12' }
        ];

        // B相单元版本信息
        const phaseBVersions = [
            { name: 'B相第1单元版本', date: '2024-12-01' },
            { name: 'B相第2单元版本', date: '2024-12-02' },
            { name: 'B相第3单元版本', date: '2024-12-03' },
            { name: 'B相第4单元版本', date: '2024-12-04' },
            { name: 'B相第5单元版本', date: '2024-12-05' },
            { name: 'B相第6单元版本', date: '2024-12-06' },
            { name: 'B相第7单元版本', date: '2024-12-07' },
            { name: 'B相第8单元版本', date: '2024-12-08' },
            { name: 'B相第9单元版本', date: '2024-12-09' },
            { name: 'B相第10单元版本', date: '2024-12-10' },
            { name: 'B相第11单元版本', date: '2024-12-11' },
            { name: 'B相第12单元版本', date: '2024-12-12' }
        ];

        // C相单元版本信息
        const phaseCVersions = [
            { name: 'C相第1单元版本', date: '2024-12-01' },
            { name: 'C相第2单元版本', date: '2024-12-02' },
            { name: 'C相第3单元版本', date: '2024-12-03' },
            { name: 'C相第4单元版本', date: '2024-12-04' },
            { name: 'C相第5单元版本', date: '2024-12-05' },
            { name: 'C相第6单元版本', date: '2024-12-06' },
            { name: 'C相第7单元版本', date: '2024-12-07' },
            { name: 'C相第8单元版本', date: '2024-12-08' },
            { name: 'C相第9单元版本', date: '2024-12-09' },
            { name: 'C相第10单元版本', date: '2024-12-10' },
            { name: 'C相第11单元版本', date: '2024-12-11' },
            { name: 'C相第12单元版本', date: '2024-12-12' }
        ];

        /**
         * 页面初始化函数
         * 在页面加载完成后执行，初始化所有版本信息表格
         */
        function initVersionPage() {
            renderDeviceInfo();
            renderPhaseVersions();
        }

        /**
         * 渲染设备信息表格
         * 生成主要设备的版本信息表格内容
         */
        function renderDeviceInfo() {
            const tbody = document.getElementById('deviceInfoBody');
            const deviceRows = deviceInfo.map(device => `
                <tr>
                    <td>${device.name}</td>
                    <td>${device.version}</td>
                    <td>${device.date}</td>
                </tr>
            `).join('');

            tbody.innerHTML = deviceRows;
        }

        /**
         * 渲染三相单元版本信息表格
         * 分别生成A、B、C三相的单元版本信息
         */
        function renderPhaseVersions() {
            // 渲染A相版本信息
            const phaseABody = document.getElementById('phaseAVersions');
            const phaseARows = phaseAVersions.map(version => `
                <tr>
                    <td>${version.name}</td>
                    <td>${version.date}</td>
                </tr>
            `).join('');
            phaseABody.innerHTML = phaseARows;

            // 渲染B相版本信息
            const phaseBBody = document.getElementById('phaseBVersions');
            const phaseBRows = phaseBVersions.map(version => `
                <tr>
                    <td>${version.name}</td>
                    <td>${version.date}</td>
                </tr>
            `).join('');
            phaseBBody.innerHTML = phaseBRows;

            // 渲染C相版本信息
            const phaseCBody = document.getElementById('phaseCVersions');
            const phaseCRows = phaseCVersions.map(version => `
                <tr>
                    <td>${version.name}</td>
                    <td>${version.date}</td>
                </tr>
            `).join('');
            phaseCBody.innerHTML = phaseCRows;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initVersionPage();
        });
    </script>
</body>
</html>
