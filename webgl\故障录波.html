<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 故障录波</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 故障录波页面专用样式
         * 基于项目统一的科技蓝色主题设计
         * 页面尺寸：1920×932像素，适配弹窗模式
         * 重新设计为通道选择+波形显示的布局
         */
        .fault-wave-container {
            width: 1920px;
            height: 932px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .fault-wave-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .fault-wave-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: var(--shadow-secondary);
            position: relative;
            z-index: 100;
        }

        .fault-wave-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--primary-color) 25%,
                var(--accent-color) 50%,
                var(--primary-color) 75%,
                transparent 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .fault-wave-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .fault-wave-title h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .fault-wave-title i {
            font-size: 32px;
            color: var(--primary-color);
            animation: logoSpin 4s linear infinite;
        }

        .fault-wave-time {
            font-family: var(--font-mono);
            font-size: 16px;
            font-weight: 600;
            color: var(--accent-color);
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
        }

        /* 主内容区域 */
        .fault-wave-main {
            height: calc(932px - 80px);
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: 20px;
            padding: 20px;
        }

        /* 左侧通道选择面板 */
        .channel-panel {
            background: rgba(26, 31, 46, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .channel-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .channel-header h3 {
            margin: 0;
            font-size: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 通道控制区域 */
        .channel-controls {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            background: rgba(0, 212, 255, 0.05);
        }

        .control-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }

        .control-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .control-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .control-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
            color: var(--bg-primary);
            font-weight: 600;
        }

        /* 通道列表 */
        .channel-list {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .channel-scroll {
            flex: 1;
            overflow-y: auto;
            padding: 8px 15px;
        }

        .channel-item {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .channel-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 3px;
            height: 100%;
            background: transparent;
            transition: all 0.3s ease;
        }

        .channel-item:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateX(3px);
        }

        .channel-item.enabled {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            border-color: var(--primary-color);
        }

        .channel-item.enabled::before {
            background: var(--channel-color, var(--primary-color));
        }

        /* 通道复选框 */
        .channel-checkbox {
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-color);
            border-radius: 3px;
            background: transparent;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .channel-checkbox.checked {
            background: var(--channel-color, var(--primary-color));
            border-color: var(--channel-color, var(--primary-color));
        }

        .channel-checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: -2px;
            left: 1px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        /* 通道颜色标识 */
        .channel-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            flex-shrink: 0;
        }

        /* 通道头部信息 */
        .channel-header {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .channel-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .channel-name {
            font-size: 13px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .channel-desc {
            font-size: 11px;
            color: var(--text-secondary);
        }

        /* 通道参数配置区域 */
        .channel-config {
            display: flex;
            flex-direction: column;
            gap: 6px;
            padding: 6px 0;
            border-top: 1px solid rgba(0, 212, 255, 0.1);
        }

        .param-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .param-select {
            flex: 1;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            outline: none;
        }

        .param-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 4px rgba(0, 212, 255, 0.3);
        }

        /* 状态指示灯 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .status-indicator.gray {
            background: #666666;
            box-shadow: 0 0 4px rgba(102, 102, 102, 0.5);
        }

        .status-indicator.green {
            background: #44ff44;
            box-shadow: 0 0 6px rgba(68, 255, 68, 0.8);
        }

        .status-indicator.red {
            background: #ff4444;
            box-shadow: 0 0 6px rgba(255, 68, 68, 0.8);
        }

        /* 通道控制按钮 */
        .channel-controls {
            display: flex;
            gap: 4px;
        }

        .channel-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: var(--text-secondary);
            padding: 3px 6px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 10px;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .channel-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .channel-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .channel-btn.record {
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .channel-btn.record:hover:not(:disabled) {
            background: rgba(68, 255, 68, 0.2);
        }

        .channel-btn.clear {
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .channel-btn.clear:hover:not(:disabled) {
            background: rgba(255, 170, 0, 0.2);
        }

        /* 通道颜色定义 */
        .channel-item[data-channel="1"] { --channel-color: #ff4444; }
        .channel-item[data-channel="2"] { --channel-color: #44ff44; }
        .channel-item[data-channel="3"] { --channel-color: #4444ff; }
        .channel-item[data-channel="4"] { --channel-color: #ffff44; }
        .channel-item[data-channel="5"] { --channel-color: #ff44ff; }
        .channel-item[data-channel="6"] { --channel-color: #44ffff; }
        .channel-item[data-channel="7"] { --channel-color: #ffaa44; }
        .channel-item[data-channel="8"] { --channel-color: #ffffff; }

        /* 右侧波形显示面板 */
        .wave-display-panel {
            background: rgba(26, 31, 46, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 波形图区域 */
        .wave-chart-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 550px;
        }

        .wave-chart-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .wave-chart-header h3 {
            margin: 0;
            font-size: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .wave-controls {
            display: flex;
            gap: 8px;
        }

        .wave-control-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .wave-control-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .wave-control-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--bg-primary);
        }

        /* 波形增强控制 */
        .wave-enhance-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: 15px;
        }

        .enhance-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .switch-container {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .switch-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: .4s;
            border-radius: 20px;
        }

        .switch-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 2px;
            bottom: 2px;
            background-color: var(--text-secondary);
            transition: .4s;
            border-radius: 50%;
        }

        .switch-input:checked + .switch-slider {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .switch-input:checked + .switch-slider:before {
            transform: translateX(18px);
            background-color: white;
        }

        /* 波形增强控制 */
        .wave-enhance-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: 15px;
        }

        .enhance-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .switch-container {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .switch-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: .4s;
            border-radius: 20px;
        }

        .switch-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 2px;
            bottom: 2px;
            background-color: var(--text-secondary);
            transition: .4s;
            border-radius: 50%;
        }

        .switch-input:checked + .switch-slider {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .switch-input:checked + .switch-slider:before {
            transform: translateX(18px);
            background-color: white;
        }

        .wave-chart-container {
            flex: 1;
            padding: 8px;
            position: relative;
            background: #000;
            border-radius: 4px;
            margin: 8px;
        }

        .wave-chart {
            width: 100%;
            height: 100%;
            min-height: 550px;
        }

        /* 波形控制面板 */
        .wave-control-panel {
            background: rgba(0, 212, 255, 0.05);
            border-top: 1px solid var(--border-color);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .playback-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .playback-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .playback-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .playback-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--bg-primary);
        }

        .time-info {
            font-family: var(--font-mono);
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 提示信息样式 */
        .help-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 11px;
            color: var(--text-secondary);
            z-index: 10;
        }

        .help-info .help-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 4px;
        }

        /* 底部操作按钮区域 */
        .fault-wave-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 100;
        }

        .footer-left,
        .footer-right {
            display: flex;
            gap: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.1) 0%,
                rgba(0, 153, 204, 0.1) 100%);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .action-btn:hover {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--bg-primary);
        }

        .action-btn.primary:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        }

        .action-btn.success {
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .action-btn.success:hover {
            background: rgba(0, 255, 136, 0.2);
            border-color: var(--success-color);
        }

        .action-btn.warning {
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .action-btn.warning:hover {
            background: rgba(255, 170, 0, 0.2);
            border-color: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="fault-wave-container">
        <!-- 顶部标题栏 -->
        <div class="fault-wave-header">
            <div class="fault-wave-title">
                <i class="fas fa-wave-square"></i>
                <h1>故障录波分析</h1>
            </div>
            <div class="fault-wave-time" id="currentTime">
                2025-01-04 15:39:21
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="fault-wave-main">
            <!-- 左侧通道选择面板 -->
            <div class="channel-panel">
                <div class="channel-header">
                    <h3>
                        <i class="fas fa-list-ol"></i>
                        通道选择
                    </h3>
                </div>

                <!-- 通道控制区域 -->
                <div class="channel-controls">
                    <div class="control-buttons">
                        <button class="control-btn" onclick="selectAllChannels()">
                            <i class="fas fa-check-double"></i>
                            全选
                        </button>
                        <button class="control-btn" onclick="clearAllChannels()">
                            <i class="fas fa-times"></i>
                            清空
                        </button>
                        <button class="control-btn" onclick="invertChannels()">
                            <i class="fas fa-exchange-alt"></i>
                            反选
                        </button>
                    </div>
                </div>

                <!-- 通道列表 -->
                <div class="channel-list">
                    <div class="channel-scroll" id="channelList">
                        <!-- 通道项目将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 右侧波形显示面板 -->
            <div class="wave-display-panel">
                <!-- 波形图显示区域 -->
                <div class="wave-chart-section">
                    <div class="wave-chart-header">
                        <h3>
                            <i class="fas fa-chart-line"></i>
                            故障录波分析
                        </h3>
                        <div class="wave-controls">
                            <button class="wave-control-btn" onclick="resetZoom()" style="display: none;">
                                <i class="fas fa-search-minus"></i>
                                重置
                            </button>
                            <button class="wave-control-btn" onclick="exportWave()">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                            <button class="wave-control-btn" onclick="toggleGrid()" style="display: none;">
                                <i class="fas fa-th"></i>
                                网格
                            </button>
                            <div class="wave-enhance-controls" style="display: none;">
                                <span class="enhance-label">曲线放大:</span>
                                <label class="switch-container">
                                    <input type="checkbox" class="switch-input" id="magnifySwitch" onchange="toggleMagnify()">
                                    <span class="switch-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="wave-chart-container">
                        <div id="waveChart" class="wave-chart"></div>
                        <div class="help-info" style="display: none;">
                            <div class="help-title">快捷键</div>
                            <div>空格: 播放/暂停</div>
                            <div>←→: 上/下一帧</div>
                            <div>ESC: 停止</div>
                        </div>
                    </div>
                </div>

                <!-- 波形控制面板 -->
                <div class="wave-control-panel" style="display: none;">
                    <div class="playback-controls">
                        <button class="playback-btn" onclick="playWave()" id="playBtn">
                            <i class="fas fa-play"></i>
                            播放
                        </button>
                        <button class="playback-btn" onclick="pauseWave()">
                            <i class="fas fa-pause"></i>
                            暂停
                        </button>
                        <button class="playback-btn" onclick="stopWave()">
                            <i class="fas fa-stop"></i>
                            停止
                        </button>
                        <button class="playback-btn" onclick="prevFrame()">
                            <i class="fas fa-step-backward"></i>
                            上一帧
                        </button>
                        <button class="playback-btn" onclick="nextFrame()">
                            <i class="fas fa-step-forward"></i>
                            下一帧
                        </button>
                    </div>
                    <div class="time-info">
                        <span id="timeInfo">时间: 0.000s / 1.000s</span>
                        <span id="statusInfo" style="margin-left: 15px; color: var(--primary-color);">● 停止</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作按钮区域 -->
        <div class="fault-wave-footer">
            <div class="footer-left">
                <button class="action-btn primary" onclick="refreshFaultList()">
                    <i class="fas fa-sync-alt"></i>
                    刷新列表
                </button>
                <button class="action-btn" onclick="clearSelection()">
                    <i class="fas fa-times"></i>
                    清除选择
                </button>
            </div>
            <div class="footer-right">
                <button class="action-btn success" onclick="exportReport()">
                    <i class="fas fa-file-export"></i>
                    导出报告
                </button>
                <button class="action-btn warning" onclick="analyzeAll()">
                    <i class="fas fa-cogs"></i>
                    批量分析
                </button>
            </div>
        </div>
    </div>

    <script>
        /**
         * 故障录波页面JavaScript功能
         * 实现通道选择、波形显示、录波回放等功能
         */

        // 全局变量
        let waveChart = null;
        let channels = [];
        let waveData = {};
        let isPlaying = false;
        let currentTime = 0;
        let totalTime = 1.0; // 总时长1秒
        let playbackTimer = null;
        let showGrid = true;
        let magnifyEnabled = false;

        // 可选参数列表
        const availableParams = [
            { value: '', label: '请选择参数' },
            { value: 'ua', label: 'A相电压' },
            { value: 'ub', label: 'B相电压' },
            { value: 'uc', label: 'C相电压' },
            { value: 'ia', label: 'A相电流' },
            { value: 'ib', label: 'B相电流' },
            { value: 'ic', label: 'C相电流' },
            { value: 'u0', label: '零序电压' },
            { value: 'i0', label: '零序电流' },
            { value: 'p', label: '有功功率' },
            { value: 'q', label: '无功功率' },
            { value: 'f', label: '频率' },
            { value: 'thd', label: '谐波畸变率' }
        ];

        // 主控板当前配置（模拟）
        const masterConfig = ['ua', 'ub', 'uc', 'ia', 'ib', 'ic', 'u0', 'i0'];

        /**
         * 页面初始化函数
         */
        function initializePage() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            initializeChannels();
            initializeWaveChart();
            generateWaveData();
            renderChannelList();
            setupEventListeners();
        }

        /**
         * 更新当前时间显示
         */
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' +
                String(now.getMonth() + 1).padStart(2, '0') + '-' +
                String(now.getDate()).padStart(2, '0') + ' ' +
                String(now.getHours()).padStart(2, '0') + ':' +
                String(now.getMinutes()).padStart(2, '0') + ':' +
                String(now.getSeconds()).padStart(2, '0');
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 初始化通道配置
         */
        function initializeChannels() {
            const channelColors = ['#ff4444', '#44ff44', '#4444ff', '#ffff44', '#ff44ff', '#44ffff', '#ffaa44', '#ffffff'];
            const defaultParams = ['ua', 'ub', 'uc', 'ia', 'ib', 'ic', 'u0', 'i0'];

            // 从本地存储加载配置
            const savedChannels = loadChannelConfig();

            channels = [];
            for (let i = 1; i <= 8; i++) {
                const savedChannel = savedChannels.find(ch => ch.id === i);
                channels.push({
                    id: i,
                    name: `通道${i}`,
                    color: channelColors[i-1],
                    enabled: savedChannel ? savedChannel.enabled : (i <= 3), // 默认启用前3个通道
                    visible: true,
                    selectedParam: savedChannel ? savedChannel.selectedParam : defaultParams[i-1],
                    paramSynced: false, // 参数同步状态
                    recordCompleted: false, // 录波完成状态
                    recordData: null // 录波数据
                });
            }

            // 验证所有通道的参数同步状态
            validateAllChannels();
        }

        /**
         * 初始化波形图表
         */
        function initializeWaveChart() {
            const chartDom = document.getElementById('waveChart');
            waveChart = echarts.init(chartDom, 'dark');

            const option = {
                backgroundColor: '#000000',
                title: {
                    text: '故障录波数据',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#00d4ff',
                        fontSize: 14
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.9)',
                    borderColor: '#00d4ff',
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = `时间: ${params[0].axisValue}ms<br/>`;
                        params.forEach(param => {
                            if (param.seriesName) {
                                result += `${param.seriesName}: ${param.value}<br/>`;
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: [],
                    top: 35,
                    textStyle: {
                        color: '#b8c5d6',
                        fontSize: 11
                    }
                },
                grid: {
                    left: '8%',
                    right: '5%',
                    bottom: '8%',
                    top: '18%',
                    containLabel: true
                },
                dataZoom: [
                    {
                        type: 'inside',
                        xAxisIndex: 0,
                        filterMode: 'none'
                    },
                    {
                        type: 'slider',
                        xAxisIndex: 0,
                        bottom: 10,
                        height: 20,
                        handleStyle: {
                            color: '#00d4ff'
                        },
                        textStyle: {
                            color: '#b8c5d6'
                        }
                    }
                ],
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: [],
                    axisLine: {
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    },
                    axisLabel: {
                        color: '#b8c5d6',
                        fontSize: 10
                    },
                    name: '时间 (ms)',
                    nameTextStyle: {
                        color: '#b8c5d6'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#00d4ff'
                        }
                    },
                    axisLabel: {
                        color: '#b8c5d6',
                        fontSize: 10
                    },
                    splitLine: {
                        show: showGrid,
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.1)',
                            type: 'dashed'
                        }
                    },
                    name: '幅值',
                    nameTextStyle: {
                        color: '#b8c5d6'
                    }
                },
                series: []
            };

            waveChart.setOption(option);
        }

        /**
         * 生成波形数据
         */
        function generateWaveData() {
            const sampleRate = 1000; // 1000个采样点/秒
            const totalSamples = Math.floor(totalTime * sampleRate);
            const timePoints = [];

            // 生成时间轴
            for (let i = 0; i < totalSamples; i++) {
                timePoints.push((i / sampleRate * 1000).toFixed(1)); // 转换为毫秒
            }

            waveData = { timePoints };

            // 为每个通道生成波形数据
            channels.forEach(channel => {
                const data = [];
                const frequency = 50; // 50Hz基频
                const amplitude = getChannelAmplitude(channel.id);
                const phase = getChannelPhase(channel.id);

                for (let i = 0; i < totalSamples; i++) {
                    const t = i / sampleRate;
                    let value = amplitude * Math.sin(2 * Math.PI * frequency * t + phase);

                    // 在0.3-0.7秒之间添加故障扰动
                    if (t >= 0.3 && t <= 0.7) {
                        const faultFactor = 1 + 0.5 * Math.sin(2 * Math.PI * 150 * t); // 150Hz扰动
                        value *= faultFactor;
                    }

                    // 添加少量噪声
                    value += (Math.random() - 0.5) * amplitude * 0.02;

                    data.push(value.toFixed(2));
                }

                waveData[`channel${channel.id}`] = data;
            });
        }

        /**
         * 获取通道幅值
         */
        function getChannelAmplitude(channelId) {
            const amplitudes = [311, 311, 311, 14.1, 14.1, 14.1, 50, 2]; // 电压和电流的典型幅值
            return amplitudes[channelId - 1] || 100;
        }

        /**
         * 获取通道相位
         */
        function getChannelPhase(channelId) {
            const phases = [0, -2*Math.PI/3, 2*Math.PI/3, Math.PI/6, Math.PI/6-2*Math.PI/3, Math.PI/6+2*Math.PI/3, 0, 0];
            return phases[channelId - 1] || 0;
        }

        /**
         * 渲染通道列表
         */
        function renderChannelList() {
            const channelListContainer = document.getElementById('channelList');
            channelListContainer.innerHTML = '';

            channels.forEach(channel => {
                const channelItem = document.createElement('div');
                channelItem.className = 'channel-item';
                channelItem.dataset.channel = channel.id;
                channelItem.style.setProperty('--channel-color', channel.color);

                if (channel.enabled) {
                    channelItem.classList.add('enabled');
                }

                // 生成参数选择选项
                const paramOptions = availableParams.map(param =>
                    `<option value="${param.value}" ${param.value === channel.selectedParam ? 'selected' : ''}>${param.label}</option>`
                ).join('');

                channelItem.innerHTML = `
                    <div class="channel-header">
                        <div class="channel-checkbox ${channel.enabled ? 'checked' : ''}"
                             onclick="toggleChannel(${channel.id})"></div>
                        <div class="channel-color" style="background-color: ${channel.color}"></div>
                        <div class="channel-info">
                            <div class="channel-name">${channel.name}</div>
                            <div class="channel-desc">参数: ${getParamLabel(channel.selectedParam)}</div>
                        </div>
                    </div>
                    <div class="channel-config">
                        <div class="param-row">
                            <select class="param-select" onchange="onParamChange(${channel.id}, this.value)">
                                ${paramOptions}
                            </select>
                            <div class="status-indicator ${channel.paramSynced ? 'green' : 'gray'}"
                                 title="${channel.paramSynced ? '参数已同步' : '参数未同步'}"></div>
                        </div>
                        <div class="channel-controls">
                            <button class="channel-btn record"
                                    onclick="startRecording(${channel.id})"
                                    ${!channel.paramSynced ? 'disabled' : ''}>
                                <i class="fas fa-record-vinyl"></i>
                                录波
                            </button>
                            <button class="channel-btn clear"
                                    onclick="clearChannelData(${channel.id})"
                                    ${!channel.recordCompleted ? 'disabled' : ''}>
                                <i class="fas fa-eraser"></i>
                                清除
                            </button>
                            <div class="status-indicator ${channel.recordCompleted ? 'red' : 'gray'}"
                                 title="${channel.recordCompleted ? '已有录波数据' : '无录波数据'}"></div>
                        </div>
                    </div>
                `;

                channelListContainer.appendChild(channelItem);
            });
        }

        /**
         * 获取参数标签
         */
        function getParamLabel(paramValue) {
            const param = availableParams.find(p => p.value === paramValue);
            return param ? param.label : '未选择';
        }

        /**
         * 参数变更处理
         */
        function onParamChange(channelId, paramValue) {
            const channel = channels.find(ch => ch.id === channelId);
            if (channel) {
                channel.selectedParam = paramValue;
                validateChannel(channel);
                renderChannelList();
                saveChannelConfig();
            }
        }

        /**
         * 验证单个通道参数
         */
        function validateChannel(channel) {
            // 检查参数是否与主控板配置一致
            channel.paramSynced = channel.selectedParam &&
                                 masterConfig.includes(channel.selectedParam);
        }

        /**
         * 验证所有通道参数
         */
        function validateAllChannels() {
            channels.forEach(channel => {
                validateChannel(channel);
            });
        }

        /**
         * 切换通道启用状态
         */
        function toggleChannel(channelId) {
            const channel = channels.find(ch => ch.id === channelId);
            if (channel) {
                channel.enabled = !channel.enabled;
                renderChannelList();
                updateWaveChart();
                saveChannelConfig();
            }
        }

        /**
         * 全选通道
         */
        function selectAllChannels() {
            channels.forEach(channel => {
                channel.enabled = true;
            });
            renderChannelList();
            updateWaveChart();
        }

        /**
         * 清空所有通道选择
         */
        function clearAllChannels() {
            channels.forEach(channel => {
                channel.enabled = false;
            });
            renderChannelList();
            updateWaveChart();
        }

        /**
         * 反选通道
         */
        function invertChannels() {
            channels.forEach(channel => {
                channel.enabled = !channel.enabled;
            });
            renderChannelList();
            updateWaveChart();
            saveChannelConfig();
        }

        /**
         * 开始录波
         */
        function startRecording(channelId) {
            const channel = channels.find(ch => ch.id === channelId);
            if (!channel || !channel.paramSynced) {
                alert('通道参数未同步，无法开始录波！');
                return;
            }

            // 模拟录波过程
            const recordBtn = document.querySelector(`[onclick="startRecording(${channelId})"]`);
            recordBtn.disabled = true;
            recordBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 录波中...';

            // 模拟录波延迟
            setTimeout(() => {
                // 生成录波数据
                channel.recordData = generateChannelRecordData(channel);
                channel.recordCompleted = true;

                // 更新界面
                renderChannelList();
                updateWaveChart();
                saveChannelConfig();

                alert(`通道${channelId}录波完成！`);
            }, 2000);
        }

        /**
         * 清除通道数据
         */
        function clearChannelData(channelId) {
            const channel = channels.find(ch => ch.id === channelId);
            if (channel) {
                channel.recordCompleted = false;
                channel.recordData = null;
                renderChannelList();
                updateWaveChart();
                saveChannelConfig();
                alert(`通道${channelId}数据已清除！`);
            }
        }

        /**
         * 生成通道录波数据
         */
        function generateChannelRecordData(channel) {
            const sampleRate = 1000;
            const totalSamples = Math.floor(totalTime * sampleRate);
            const data = [];

            const amplitude = getChannelAmplitude(channel.id);
            const phase = getChannelPhase(channel.id);
            const frequency = 50;

            for (let i = 0; i < totalSamples; i++) {
                const t = i / sampleRate;
                let value = amplitude * Math.sin(2 * Math.PI * frequency * t + phase);

                // 添加故障扰动
                if (t >= 0.3 && t <= 0.7) {
                    const faultFactor = 1 + 0.8 * Math.sin(2 * Math.PI * 150 * t);
                    value *= faultFactor;
                }

                // 添加噪声
                value += (Math.random() - 0.5) * amplitude * 0.03;
                data.push(value.toFixed(2));
            }

            return data;
        }

        /**
         * 保存通道配置到本地存储
         */
        function saveChannelConfig() {
            const config = channels.map(ch => ({
                id: ch.id,
                enabled: ch.enabled,
                selectedParam: ch.selectedParam,
                recordCompleted: ch.recordCompleted
            }));
            localStorage.setItem('faultWaveChannelConfig', JSON.stringify(config));
        }

        /**
         * 从本地存储加载通道配置
         */
        function loadChannelConfig() {
            try {
                const config = localStorage.getItem('faultWaveChannelConfig');
                return config ? JSON.parse(config) : [];
            } catch (e) {
                console.warn('加载通道配置失败:', e);
                return [];
            }
        }

        /**
         * 更新波形图表
         */
        function updateWaveChart() {
            if (!waveChart || !waveData.timePoints) return;

            const enabledChannels = channels.filter(ch => ch.enabled);
            const legendData = enabledChannels.map(ch => getParamLabel(ch.selectedParam));
            const series = [];

            enabledChannels.forEach(channel => {
                // 优先使用录波数据，否则使用模拟数据
                const channelData = channel.recordCompleted && channel.recordData ?
                                   channel.recordData :
                                   waveData[`channel${channel.id}`];

                if (channelData) {
                    const lineWidth = magnifyEnabled ? 2.5 : 1.5;
                    series.push({
                        name: getParamLabel(channel.selectedParam),
                        type: 'line',
                        data: channelData,
                        smooth: false,
                        lineStyle: {
                            color: channel.color,
                            width: lineWidth
                        },
                        symbol: 'none',
                        sampling: 'lttb' // 大数据量优化
                    });
                }
            });

            const option = {
                legend: {
                    data: legendData
                },
                xAxis: {
                    data: waveData.timePoints
                },
                yAxis: {
                    splitLine: {
                        show: showGrid,
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.1)',
                            type: 'dashed'
                        }
                    }
                },
                series: series
            };

            waveChart.setOption(option, true);
        }

        /**
         * 播放波形
         */
        function playWave() {
            if (isPlaying) return;

            isPlaying = true;
            document.getElementById('playBtn').innerHTML = '<i class="fas fa-pause"></i> 播放中';
            updateStatusInfo('● 播放中');

            playbackTimer = setInterval(() => {
                currentTime += 0.01; // 每10ms前进
                if (currentTime >= totalTime) {
                    currentTime = 0; // 循环播放
                }
                updateTimeInfo();
                updateWaveDisplay();
            }, 10);
        }

        /**
         * 暂停波形
         */
        function pauseWave() {
            if (!isPlaying) return;

            isPlaying = false;
            clearInterval(playbackTimer);
            document.getElementById('playBtn').innerHTML = '<i class="fas fa-play"></i> 播放';
            updateStatusInfo('● 暂停');
        }

        /**
         * 停止波形
         */
        function stopWave() {
            pauseWave();
            currentTime = 0;
            updateTimeInfo();
            updateWaveDisplay();
            updateStatusInfo('● 停止');
        }

        /**
         * 上一帧
         */
        function prevFrame() {
            currentTime = Math.max(0, currentTime - 0.001);
            updateTimeInfo();
            updateWaveDisplay();
        }

        /**
         * 下一帧
         */
        function nextFrame() {
            currentTime = Math.min(totalTime, currentTime + 0.001);
            updateTimeInfo();
            updateWaveDisplay();
        }

        /**
         * 更新时间信息显示
         */
        function updateTimeInfo() {
            const timeInfoElement = document.getElementById('timeInfo');
            timeInfoElement.textContent = `时间: ${currentTime.toFixed(3)}s / ${totalTime.toFixed(3)}s`;
        }

        /**
         * 更新状态信息显示
         */
        function updateStatusInfo(status) {
            const statusInfoElement = document.getElementById('statusInfo');
            statusInfoElement.textContent = status;
        }

        /**
         * 更新波形显示（用于播放时的动态效果）
         */
        function updateWaveDisplay() {
            // 这里可以添加播放时的特殊效果，比如高亮当前时间点
            // 暂时保持静态显示
        }

        /**
         * 设置事件监听器
         */
        function setupEventListeners() {
            // 窗口大小改变时重新调整图表
            window.addEventListener('resize', function() {
                if (waveChart) {
                    waveChart.resize();
                }
            });

            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                switch(e.key) {
                    case ' ': // 空格键播放/暂停
                        e.preventDefault();
                        if (isPlaying) {
                            pauseWave();
                        } else {
                            playWave();
                        }
                        break;
                    case 'ArrowLeft': // 左箭头上一帧
                        e.preventDefault();
                        prevFrame();
                        break;
                    case 'ArrowRight': // 右箭头下一帧
                        e.preventDefault();
                        nextFrame();
                        break;
                    case 'Escape': // ESC停止
                        e.preventDefault();
                        stopWave();
                        break;
                }
            });
        }

        /**
         * 重置波形图缩放
         */
        function resetZoom() {
            if (waveChart) {
                waveChart.dispatchAction({
                    type: 'dataZoom',
                    start: 0,
                    end: 100
                });
            }
        }

        /**
         * 导出波形图
         */
        function exportWave() {
            if (waveChart) {
                const url = waveChart.getDataURL({
                    pixelRatio: 2,
                    backgroundColor: '#000000'
                });

                const link = document.createElement('a');
                const now = new Date();
                const timestamp = now.toISOString().slice(0, 19).replace(/[:\s]/g, '_');
                link.download = `故障录波_${timestamp}.png`;
                link.href = url;
                link.click();
            } else {
                alert('波形图表未初始化');
            }
        }

        /**
         * 切换网格显示
         */
        function toggleGrid() {
            showGrid = !showGrid;
            const option = {
                yAxis: {
                    splitLine: {
                        show: showGrid,
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.1)',
                            type: 'dashed'
                        }
                    }
                }
            };
            waveChart.setOption(option);
        }

        /**
         * 切换曲线放大
         */
        function toggleMagnify() {
            magnifyEnabled = !magnifyEnabled;
            updateWaveChart();
        }

        /**
         * 刷新数据
         */
        function refreshFaultList() {
            generateWaveData();
            updateWaveChart();
            stopWave();
            alert('数据已刷新');
        }

        /**
         * 清除选择
         */
        function clearSelection() {
            clearAllChannels();
            stopWave();
        }

        /**
         * 导出故障报告
         */
        function exportReport() {
            const enabledChannels = channels.filter(ch => ch.enabled);
            if (enabledChannels.length === 0) {
                alert('请先选择要分析的通道');
                return;
            }

            // 生成报告内容
            const now = new Date();
            const reportContent = `
故障录波分析报告
================

录波基本信息：
- 生成时间: ${now.toLocaleString()}
- 录波时长: ${totalTime}秒
- 采样频率: 1000Hz
- 启用通道: ${enabledChannels.map(ch => ch.description).join(', ')}

通道详细信息：
${enabledChannels.map(ch => `- ${ch.name} (${ch.description}): 颜色 ${ch.color}`).join('\n')}

分析结论：
本次录波数据包含${enabledChannels.length}个通道的波形数据，
在0.3-0.7秒时间段内检测到故障扰动信号。
建议进一步分析故障原因并采取相应措施。

报告生成时间: ${now.toLocaleString()}
            `;

            // 创建并下载文件
            const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            const timestamp = now.toISOString().slice(0, 19).replace(/[:\s]/g, '_');
            link.download = `故障录波报告_${timestamp}.txt`;
            link.href = url;
            link.click();
            URL.revokeObjectURL(url);
        }

        /**
         * 批量分析
         */
        function analyzeAll() {
            const enabledChannels = channels.filter(ch => ch.enabled);
            if (enabledChannels.length === 0) {
                alert('请先选择要分析的通道');
                return;
            }

            let analysisResult = '批量分析结果：\n\n';
            analysisResult += `总通道数量: ${channels.length}\n`;
            analysisResult += `启用通道数量: ${enabledChannels.length}\n\n`;

            analysisResult += '启用通道列表：\n';
            enabledChannels.forEach(ch => {
                analysisResult += `- ${ch.name} (${ch.description}): ${ch.color}\n`;
            });

            analysisResult += '\n分析建议：\n';
            analysisResult += '- 检查故障时段(0.3-0.7s)的波形异常\n';
            analysisResult += '- 对比各通道波形的相位关系\n';
            analysisResult += '- 分析谐波含量和频谱特征\n';

            alert(analysisResult);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            // 初始化完成后更新波形显示
            setTimeout(() => {
                updateWaveChart();
                updateTimeInfo();
                updateStatusInfo('● 停止');
            }, 100);
        });
    </script>
</body>
</html>
