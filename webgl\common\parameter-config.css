/**
 * 通用参数配置页面样式
 * 用于支持多个类似的参数配置页面
 * 包含工业监控界面的科技感设计
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
    background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow: auto;
    position: relative;
}

/* 科技感背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.08) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px;
}

.header {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px 0;
    border-bottom: 2px solid rgba(0, 212, 255, 0.4);
}

.header h1 {
    font-size: 28px;
    color: #00d4ff;
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
    font-weight: bold;
}

.main-content {
    flex: 1;
    display: flex;
    gap: 15px;
    position: relative;
    padding-bottom: 70px; /* 为底部按钮预留空间 */
}

.protection-panel {
    flex: 1;
    background: rgba(26, 31, 46, 0.95);
    border: 2px solid rgba(0, 212, 255, 0.4);
    border-radius: 8px;
    padding: 15px;
    overflow: visible;
    min-width: 0; /* 防止内容溢出影响平分 */
}

.panel-title {
    font-size: 18px;
    color: #00d4ff;
    text-align: center;
    margin-bottom: 15px;
    padding: 8px 0;
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 4px;
    font-weight: bold;
}

.params-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 18px; /* 适度缩小字体 */
}

.params-table th {
    background: rgba(0, 212, 255, 0.3);
    color: #00d4ff;
    padding: 8px 6px; /* 适度缩小内边距 */
    text-align: center;
    border: 1px solid rgba(0, 212, 255, 0.4);
    font-weight: bold;
    font-size: 16px; /* 适度缩小表头字体 */
}

.params-table td {
    padding: 6px 8px; /* 适度缩小内边距 */
    text-align: center;
    border: 1px solid rgba(0, 212, 255, 0.2);
    background: rgba(42, 49, 66, 0.7);
    vertical-align: middle;
    height: 46px; /* 适度缩小行高 */
}

.params-table tr:hover td {
    background: rgba(42, 49, 66, 0.9);
    border-color: rgba(0, 212, 255, 0.4);
}

.param-index {
    font-size: 16px; /* 适度缩小序号字体 */
    color: #7a8ba0;
    font-weight: bold;
    width: 42px; /* 适度缩小宽度 */
    text-align: right;
    padding-right: 8px;
}

.param-name {
    font-size: 15px; /* 适度缩小参数名称字体 */
    color: #ffffff;
    text-align: left;
    padding-left: 6px;
    line-height: 1.3; /* 适度缩小行高 */
    max-width: 200px;
    word-wrap: break-word;
    overflow: visible;
    white-space: normal;
}

.param-current {
    font-size: 16px; /* 适度缩小当前值字体 */
    color: #00d4ff;
    font-weight: bold;
    width: 55px; /* 适度缩小宽度 */
}

.param-setting {
    width: 60px; /* 增大设置列宽度 */
}

.toggle-switch {
    position: relative;
    width: 60px; /* 增大开关宽度 */
    height: 30px; /* 增大开关高度 */
    background: #dc3545;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 212, 255, 0.3);
    margin: 0 auto;
}

.toggle-switch.active {
    background: #007bff;
}

.toggle-switch::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px; /* 增大滑块 */
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.toggle-switch.active::before {
    transform: translateX(30px); /* 调整滑块位置 */
}

.send-button {
    position: fixed; /* 改为fixed确保始终可见 */
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
}

.send-button:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
    transform: translateX(-50%) translateY(-2px);
}

.send-button:active {
    transform: translateX(-50%) translateY(0);
    box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.send-button:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    cursor: not-allowed;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.send-button::before {
    content: "📤";
    font-size: 18px;
}

/* 状态提示样式 */
.status-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(26, 31, 46, 0.95);
    border: 2px solid rgba(0, 212, 255, 0.4);
    border-radius: 8px;
    padding: 20px 30px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    z-index: 2000;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    display: none;
}

.status-message.success {
    border-color: rgba(40, 167, 69, 0.6);
    color: #28a745;
}

.status-message.error {
    border-color: rgba(220, 53, 69, 0.6);
    color: #dc3545;
}

.status-message.warning {
    border-color: rgba(255, 193, 7, 0.6);
    color: #ffc107;
}

/* 参数修改高亮样式 */
.modified-param {
    background-color: rgba(255, 193, 7, 0.3) !important;
    transition: background-color 0.3s ease;
}

.params-table tr:hover .modified-param {
    background-color: rgba(255, 193, 7, 0.4) !important;
}

/* MQTT 连接状态指示器样式 */
.mqtt-status-container {
    position: fixed;
    top: 15px;
    right: 15px;
    z-index: 1000;
    display: flex;
    gap: 10px;
    align-items: center;
}

.mqtt-connection-status {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    display: inline-block;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.mqtt-connection-status.connected {
    background-color: #4CAF50;
    border-color: #45a049;
}

.mqtt-connection-status.disconnected {
    background-color: #f44336;
    border-color: #da190b;
}

.data-timestamp {
    font-size: 11px;
    color: #7a8ba0;
    padding: 4px 8px;
    background: rgba(26, 31, 46, 0.8);
    border-radius: 4px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}
