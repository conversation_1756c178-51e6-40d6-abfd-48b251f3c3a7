/**
 * 通用参数配置管理器
 * 支持通过配置传入不同的参数列表，实现可复用的参数配置功能
 * 包含MQTT连接、数据处理、界面更新等完整功能
 */

/**
 * 通用参数配置管理类
 * 负责数据处理和界面更新
 */
class ParameterConfigManager {
    /**
     * 构造函数
     * @param {Object} config - 配置对象
     * @param {Array} config.parameters - 参数定义数组，格式：[{mqttId, name}, ...]
     * @param {string} config.pageTitle - 页面标题
     * @param {Array} config.panelTitles - 面板标题数组
     * @param {number} config.parametersPerPanel - 每个面板的参数数量
     * @param {number} config.parametersPerRow - 每行显示的参数数量（默认2）
     */
    constructor(config) {
        this.config = config;
        this.parameters = [];
        this.currentValues = {};
        this.modifiedValues = {};

        this.initializeParams();
        this.createParamTables();
    }

    /**
     * 初始化参数定义
     * 根据配置创建参数对象
     */
    initializeParams() {
        if (!this.config.parameters || !Array.isArray(this.config.parameters)) {
            console.error('参数配置无效：parameters 必须是数组');
            return;
        }

        const parametersPerPanel = this.config.parametersPerPanel || Math.ceil(this.config.parameters.length / 2);
        
        this.config.parameters.forEach((param, index) => {
            const panelIndex = Math.floor(index / parametersPerPanel) + 1;
            const paramIndex = (index % parametersPerPanel) + 1;
            
            this.parameters.push({
                id: `param_${index + 1}`,
                index: paramIndex,
                mqttId: param.mqttId,
                name: param.name,
                currentValue: 0,
                settingValue: 0,
                panel: panelIndex,
                isInitialized: false // 首次同步标记
            });
        });

        console.log(`初始化了 ${this.parameters.length} 个参数`);
    }

    /**
     * 创建参数表格界面
     */
    createParamTables() {
        const parametersPerPanel = this.config.parametersPerPanel || Math.ceil(this.config.parameters.length / 2);
        const parametersPerRow = this.config.parametersPerRow || 2;
        const panelCount = Math.ceil(this.parameters.length / parametersPerPanel);

        // 更新页面标题
        if (this.config.pageTitle) {
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = this.config.pageTitle;
            }
        }

        // 更新面板标题并创建表格
        for (let panelIndex = 1; panelIndex <= panelCount; panelIndex++) {
            const panelTitle = document.querySelector(`.protection-panel:nth-child(${panelIndex}) .panel-title`);
            const tableBody = document.getElementById(`protection-table-${panelIndex}`);
            
            if (panelTitle && this.config.panelTitles && this.config.panelTitles[panelIndex - 1]) {
                panelTitle.textContent = this.config.panelTitles[panelIndex - 1];
            }

            if (tableBody) {
                // 获取当前面板的参数
                const panelParams = this.parameters.filter(p => p.panel === panelIndex);
                
                // 按行创建表格
                for (let i = 0; i < panelParams.length; i += parametersPerRow) {
                    const rowParams = [];
                    for (let j = 0; j < parametersPerRow && (i + j) < panelParams.length; j++) {
                        rowParams.push(panelParams[i + j]);
                    }
                    
                    const row = this.createParamTableRow(rowParams);
                    tableBody.appendChild(row);
                }
            }
        }

        // 初始化所有参数的高亮状态
        this.initializeAllHighlights();
    }

    /**
     * 创建单个参数表格行
     * @param {Array} params - 参数数组（支持1-4个参数）
     */
    createParamTableRow(params) {
        const row = document.createElement('tr');
        let rowHTML = '';

        params.forEach(param => {
            rowHTML += `
                <td class="param-index" data-param-id="${param.id}" data-cell-type="index">${param.index}</td>
                <td class="param-name" data-param-id="${param.id}" data-cell-type="name">${param.name}</td>
                <td class="param-setting" data-param-id="${param.id}" data-cell-type="setting">
                    <div class="toggle-switch ${param.settingValue ? 'active' : ''}"
                         id="toggle-${param.id}"
                         onclick="parameterManager.toggleParam('${param.id}')">
                    </div>
                </td>
                <td class="param-current" data-param-id="${param.id}" data-cell-type="current" id="current-${param.id}">${param.currentValue}</td>
            `;
        });

        row.innerHTML = rowHTML;
        return row;
    }

    /**
     * 切换参数设定值
     * @param {string} paramId - 参数ID
     */
    toggleParam(paramId) {
        const param = this.parameters.find(p => p.id === paramId);
        if (!param) return;

        // 切换设定值
        param.settingValue = param.settingValue ? 0 : 1;

        // 更新界面
        const toggle = document.getElementById(`toggle-${paramId}`);
        if (param.settingValue) {
            toggle.classList.add('active');
        } else {
            toggle.classList.remove('active');
        }

        // 更新高亮状态
        this.updateHighlightStatus(paramId);

        // 记录修改
        this.modifiedValues[paramId] = param.settingValue;

        console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
    }

    /**
     * 更新参数高亮状态
     * 当设定值与当前值不一致时高亮显示相关单元格
     * 修复浮点数精度问题：对于数值类型，使用格式化后的值进行比较
     * @param {string} paramId - 参数ID
     */
    updateHighlightStatus(paramId) {
        const param = this.parameters.find(p => p.id === paramId);
        if (!param) return;

        // 检查是否需要高亮 - 处理浮点数精度问题
        let needsHighlight = false;
        
        const settingValue = param.settingValue;
        const currentValue = param.currentValue;
        
        // 检查是否为数值类型（包括整数和浮点数）
        if (typeof settingValue === 'number' && typeof currentValue === 'number') {
            // 数值类型：使用格式化后的值进行比较，避免浮点数精度问题
            const formattedSetting = parseFloat(settingValue).toFixed(2);
            const formattedCurrent = parseFloat(currentValue).toFixed(2);
            needsHighlight = formattedSetting !== formattedCurrent;
        } else {
            // 非数值类型：使用原始比较逻辑
            needsHighlight = settingValue !== currentValue;
        }

        // 获取所有需要高亮的单元格
        const cells = document.querySelectorAll(`[data-param-id="${paramId}"]`);
        
        cells.forEach(cell => {
            if (needsHighlight) {
                cell.classList.add('modified-param');
            } else {
                cell.classList.remove('modified-param');
            }
        });
    }

    /**
     * 初始化所有参数的高亮状态
     * 在页面加载和表格创建后调用
     */
    initializeAllHighlights() {
        this.parameters.forEach(param => {
            this.updateHighlightStatus(param.id);
        });
    }

    /**
     * 更新参数当前值（从 MQTT 数据）
     * 使用 MQTT 标识符 ID 直接匹配参数
     * @param {string} mqttId - MQTT 参数标识符
     * @param {number} value - 参数值
     */
    updateCurrentValueFromMQTT(mqttId, value) {
        // 直接通过 MQTT ID 查找对应的参数
        const param = this.parameters.find(p => p.mqttId === mqttId);

        if (param) {
            // 更新参数当前值
            const oldCurrentValue = param.currentValue;
            param.currentValue = value;

            // 首次数据同步：如果参数未初始化，将设定值设为当前值
            if (!param.isInitialized) {
                const oldSettingValue = param.settingValue;
                param.settingValue = value;
                param.isInitialized = true;

                // 更新界面上的设定值显示（开关状态）
                const toggleElement = document.getElementById(`toggle-${param.id}`);
                if (toggleElement) {
                    if (value) {
                        toggleElement.classList.add('active');
                    } else {
                        toggleElement.classList.remove('active');
                    }
                }

                // 记录修改
                this.modifiedValues[param.id] = param.settingValue;

                console.log(`首次同步参数 ${param.name}: 当前值=${value}, 设定值=${value} (已初始化)`);
            }

            // 更新界面显示（当前值）
            const currentElement = document.getElementById(`current-${param.id}`);
            if (currentElement) {
                // 检查是否为浮点数类型，如果是则格式化为2位小数
                let displayValue;
                if (typeof value === 'number' && !Number.isInteger(value)) {
                    // 浮点数，格式化为2位小数
                    displayValue = value.toFixed(2);
                } else if (typeof value === 'number') {
                    // 整数，但为了一致性也显示为2位小数
                    displayValue = value.toFixed(2);
                } else {
                    // 其他类型，直接显示
                    displayValue = value;
                }

                currentElement.textContent = displayValue;
                currentElement.style.color = '#00d4ff'; // 恢复正常颜色
            }

            // 更新高亮状态
            this.updateHighlightStatus(param.id);
        }
    }

    /**
     * 批量更新 MQTT 数据（旧格式 - 嵌套对象）
     * @param {Object} mqttData - MQTT 数据对象
     */
    updateFromMQTTData(mqttData) {
        if (!mqttData || !mqttData.properties) {
            console.warn('MQTT 数据格式无效');
            return;
        }

        let updatedCount = 0;
        let unmatchedCount = 0;
        
        Object.keys(mqttData.properties).forEach(propertyId => {
            const propertyData = mqttData.properties[propertyId];
            const value = propertyData.value;

            // 直接使用 MQTT ID 查找参数
            const param = this.parameters.find(p => p.mqttId === propertyId);
            if (param) {
                this.updateCurrentValueFromMQTT(propertyId, value);
                updatedCount++;
            } else {
                unmatchedCount++;
            }
        });

        console.log(`MQTT 批量更新完成，共更新 ${updatedCount} 个参数，未匹配 ${unmatchedCount} 个`);

        // 更新数据时间戳
        updateDataTimestamp(new Date());
    }

    /**
     * 批量更新 MQTT 数据（新格式 - JSON 数组）
     * 使用 MQTT 标识符 ID 直接匹配参数
     * @param {Array} mqttArray - MQTT 数据数组
     */
    updateFromJSONArray(mqttArray) {
        if (!Array.isArray(mqttArray) || mqttArray.length === 0) {
            console.warn('MQTT 数据格式无效：应为非空数组');
            return;
        }

        let updatedCount = 0;
        let invalidCount = 0;
        let unmatchedCount = 0;

        mqttArray.forEach(item => {
            if (!item || typeof item !== 'object') {
                invalidCount++;
                return;
            }

            // 支持两种格式：
            // 1. {id, value} 格式
            // 2. {id, name, ts, value} 格式（用户提供的格式）
            let id = item.id;
            let value = item.value;

            if (!id || value === undefined || value === null) {
                invalidCount++;
                console.warn('MQTT 数据项格式无效：缺少 id 或 value 字段', item);
                return;
            }

            // 处理字符串类型的值，支持浮点数
            let numericValue;
            if (typeof value === 'string') {
                // 使用 parseFloat 而不是 parseInt 来支持浮点数
                numericValue = parseFloat(value);
            } else if (typeof value === 'number') {
                numericValue = value;
            } else {
                invalidCount++;
                console.warn(`MQTT 数据值类型无效: ${typeof value} (id: ${id})`);
                return;
            }

            if (isNaN(numericValue)) {
                invalidCount++;
                console.warn(`MQTT 数据值转换失败: ${value} (id: ${id})`);
                return;
            }

            // 直接使用 MQTT ID 查找参数
            const param = this.parameters.find(p => p.mqttId === id);
            if (param) {
                this.updateCurrentValueFromMQTT(id, numericValue);
                updatedCount++;
            } else {
                unmatchedCount++;
            }
        });

        console.log(`MQTT 批量更新完成：更新 ${updatedCount} 个参数，无效 ${invalidCount} 个，未匹配 ${unmatchedCount} 个`);

        // 更新数据时间戳
        updateDataTimestamp(new Date());
    }

    /**
     * 获取所有配置数据
     */
    getConfigurationData() {
        return {
            timestamp: new Date().toISOString(),
            parameters: this.parameters.map(param => ({
                id: param.id,
                index: param.index,
                name: param.name,
                currentValue: param.currentValue,
                settingValue: param.settingValue,
                panel: param.panel,
                isInitialized: param.isInitialized,
                mqttId: param.mqttId
            })),
            modifiedValues: this.modifiedValues
        };
    }

    /**
     * 获取需要发送的参数设置（设定值与当前值不一致的参数）
     * @returns {Array} 需要发送的参数数组
     */
    getModifiedParameters() {
        const modifiedParams = [];

        this.parameters.forEach(param => {
            if (param.settingValue !== param.currentValue) {
                modifiedParams.push({
                    id: param.mqttId,
                    value: param.settingValue.toString(), // 转换为字符串格式
                    name: param.name, // 用于日志显示
                    currentValue: param.currentValue,
                    settingValue: param.settingValue
                });
            }
        });

        return modifiedParams;
    }

    /**
     * 获取用于 MQTT 发送的参数数组（仅包含 id 和 value）
     * @returns {Array} MQTT 发送格式的参数数组
     */
    getMQTTParameterArray() {
        const modifiedParams = this.getModifiedParameters();
        return modifiedParams.map(param => ({
            id: param.id,
            value: param.value
        }));
    }

    /**
     * 从保存的配置数据中恢复参数状态
     * @param {Object} savedData - 保存的配置数据
     */
    restoreFromConfigurationData(savedData) {
        if (!savedData || !savedData.parameters) {
            console.warn('无效的配置数据格式');
            return;
        }

        savedData.parameters.forEach(savedParam => {
            const param = this.parameters.find(p => p.id === savedParam.id);
            if (param) {
                // 恢复设定值和初始化状态
                param.settingValue = savedParam.settingValue;
                param.isInitialized = savedParam.isInitialized || false;

                // 更新界面显示
                const toggleElement = document.getElementById(`toggle-${param.id}`);
                if (toggleElement) {
                    if (param.settingValue) {
                        toggleElement.classList.add('active');
                    } else {
                        toggleElement.classList.remove('active');
                    }
                }
            }
        });

        // 恢复修改记录
        if (savedData.modifiedValues) {
            Object.assign(this.modifiedValues, savedData.modifiedValues);
        }

        console.log(`已从保存的配置数据中恢复 ${savedData.parameters.length} 个参数状态`);
    }
}

/**
 * MQTT 参数数据管理器
 * 负责连接 MQTT 服务器并处理参数相关数据
 */
class MQTTParameterManager {
    constructor() {
        this.mqttClient = null;
        this.isConnected = false;
        this.subscriptionTopic = '/197/D19EOEN59V1MJ/ws/service';
        this.publishTopic = '/197/D19EOEN59V1MJ/function/get';
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000;
        this.lastDataUpdate = null;
        this.messageCount = 0;

        this.init();
    }

    /**
     * 初始化 MQTT 连接
     */
    async init() {
        console.log('初始化 MQTT 参数数据管理器...');
        try {
            await this.connectMQTT();
            await this.subscribeToTopic();
            this.setupMessageHandler();
            this.setupReconnectHandler();
            console.log('MQTT 参数数据管理器初始化完成');
        } catch (error) {
            console.error('MQTT 参数数据管理器初始化失败:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * 连接 MQTT 服务器
     */
    async connectMQTT() {
        return new Promise((resolve, reject) => {
            try {
                // 使用与电气系统相同的 MQTT 配置
                const options = {
                    username: 'FastBee',
                    password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.g1HCISIvQd6YkNgWhblKnXqHeRI74lnP1F8qZOd9XV5a7J41Qi77f9jLxxWd_EVN0XJPP1haYeRK3Uz_xrbEuA',
                    cleanSession: true,
                    keepAlive: 30,
                    clientId: 'web-' + Math.random().toString(16).substr(2),
                    connectTimeout: 60000,
                };

                // 使用已验证的 MQTT 服务器地址
                const url = 'wss://mqtt.qizhiyun.cc/mqtt';
                console.log('连接到 MQTT 服务器（参数配置）：', url);

                // 使用全局 mqtt 对象连接
                if (typeof mqtt !== 'undefined') {
                    this.mqttClient = mqtt.connect(url, options);
                } else {
                    throw new Error('MQTT 客户端库未加载');
                }

                this.mqttClient.on('connect', () => {
                    console.log('MQTT 参数配置连接成功');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    updateMQTTStatus('connected', 'MQTT 已连接');
                    resolve();
                });

                this.mqttClient.on('error', (error) => {
                    console.error('MQTT 参数配置连接失败:', error);
                    this.isConnected = false;
                    updateMQTTStatus('disconnected', `MQTT 连接失败: ${error.message}`);
                    reject(error);
                });

                this.mqttClient.on('close', () => {
                    console.log('MQTT 参数配置连接已断开');
                    this.isConnected = false;
                    updateMQTTStatus('disconnected', 'MQTT 连接已断开');
                });

            } catch (error) {
                console.error('MQTT 连接配置错误:', error);
                reject(error);
            }
        });
    }

    /**
     * 订阅参数数据主题
     */
    async subscribeToTopic() {
        return new Promise((resolve, reject) => {
            if (!this.mqttClient || !this.isConnected) {
                reject(new Error('MQTT 客户端未连接'));
                return;
            }

            this.mqttClient.subscribe(this.subscriptionTopic, { qos: 1 }, (err, granted) => {
                if (err) {
                    console.error('订阅参数主题失败:', err);
                    reject(err);
                } else {
                    console.log('成功订阅参数主题:', this.subscriptionTopic);
                    resolve(granted);
                }
            });
        });
    }

    /**
     * 设置消息处理器
     */
    setupMessageHandler() {
        if (!this.mqttClient) return;

        this.mqttClient.on('message', (topic, message) => {
            try {
                if (topic === this.subscriptionTopic) {
                    this.messageCount++;
                    const data = JSON.parse(message.toString());
                    console.log(`收到参数数据 (#${this.messageCount}):`, data);

                    // 记录原始数据用于调试
                    console.log('原始消息长度:', message.length, '字节');

                    this.processParameterData(data);
                }
            } catch (error) {
                console.error('处理 MQTT 消息失败:', error);
                console.error('原始消息:', message.toString());
            }
        });
    }

    /**
     * 设置重连处理器
     */
    setupReconnectHandler() {
        if (!this.mqttClient) return;

        this.mqttClient.on('reconnect', () => {
            console.log('正在重连 MQTT 参数配置...');
            this.reconnectAttempts++;
            updateMQTTStatus('disconnected', `重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        });

        this.mqttClient.on('offline', () => {
            console.log('MQTT 参数配置离线');
            this.isConnected = false;
            this.scheduleReconnect();
        });
    }

    /**
     * 处理参数数据（支持多种数据格式）
     */
    processParameterData(data) {
        if (window.parameterManager) {
            if (data && data.message && Array.isArray(data.message)) {
                // 处理包含 message 数组的格式（实际接收到的格式）
                console.log('处理 message 数组格式数据，数组长度:', data.message.length);
                window.parameterManager.updateFromJSONArray(data.message);
            } else {
                console.warn('未识别的数据格式:', data);
            }
            this.lastDataUpdate = new Date();
        } else {
            console.warn('参数管理器未初始化，无法处理数据');
        }
    }

    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error(`MQTT 重连次数超过限制 (${this.maxReconnectAttempts})，停止自动重连`);
            updateMQTTStatus('disconnected', 'MQTT 连接失败');
            return;
        }

        const backoffDelay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts), 60000);
        console.log(`第 ${this.reconnectAttempts + 1} 次尝试重连 MQTT 参数配置，${backoffDelay/1000}秒后重试...`);

        setTimeout(() => {
            this.init();
        }, backoffDelay);
    }

    /**
     * 手动重连
     */
    async reconnect() {
        if (this.mqttClient) {
            this.mqttClient.end();
        }
        this.reconnectAttempts = 0;
        await this.init();
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.mqttClient) {
            this.mqttClient.end();
            this.mqttClient = null;
        }
        this.isConnected = false;
        console.log('MQTT 参数配置连接已断开');
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            lastDataUpdate: this.lastDataUpdate,
            subscriptionTopic: this.subscriptionTopic
        };
    }

    /**
     * 发送参数设置到 MQTT 服务器
     * @param {Array} parameterArray - 参数数组，格式：[{id, value}, ...]
     * @returns {Promise} 发送结果
     */
    async sendParameterSettings(parameterArray) {
        return new Promise((resolve, reject) => {
            if (!this.mqttClient || !this.isConnected) {
                reject(new Error('MQTT 客户端未连接'));
                return;
            }

            if (!Array.isArray(parameterArray) || parameterArray.length === 0) {
                reject(new Error('参数数组为空或格式无效'));
                return;
            }

            // 转换为 JSON 字符串
            const messagePayload = JSON.stringify(parameterArray);

            console.log(`发送参数设置到主题: ${this.publishTopic}`);
            console.log(`发送数据:`, parameterArray);

            // 发布消息
            this.mqttClient.publish(this.publishTopic, messagePayload, { qos: 1 }, (error) => {
                if (error) {
                    console.error('MQTT 发送失败:', error);
                    reject(error);
                } else {
                    console.log('MQTT 参数设置发送成功');
                    resolve({
                        success: true,
                        topic: this.publishTopic,
                        parameterCount: parameterArray.length,
                        timestamp: new Date()
                    });
                }
            });
        });
    }
}

// 全局变量
let parameterManager = null;
let mqttParameterManager = null;

/**
 * 更新 MQTT 连接状态显示
 * @param {string} status - 连接状态：connected 或 disconnected
 * @param {string} message - 状态消息
 */
function updateMQTTStatus(status, message) {
    const statusElement = document.getElementById('mqtt-status');
    if (statusElement) {
        statusElement.className = `mqtt-connection-status ${status}`;
        statusElement.textContent = message;
    }
}

/**
 * 更新数据时间戳
 * @param {Date} timestamp - 时间戳
 * @param {string} customMessage - 自定义消息
 */
function updateDataTimestamp(timestamp, customMessage = null) {
    const timestampElement = document.getElementById('data-timestamp');
    if (timestampElement) {
        if (customMessage) {
            timestampElement.textContent = customMessage;
        } else if (timestamp) {
            const timeStr = timestamp.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timestampElement.textContent = `数据更新: ${timeStr}`;
        } else {
            timestampElement.textContent = '等待数据...';
        }
    }
}

/**
 * 更新发送按钮状态
 */
function updateSendButtonStatus() {
    const sendButton = document.getElementById('send-settings-btn');
    if (!sendButton) return;

    if (mqttParameterManager) {
        const status = mqttParameterManager.getConnectionStatus();
        if (status.isConnected) {
            sendButton.disabled = false;
            sendButton.title = '发送参数设置到 MQTT 服务器';
        } else {
            sendButton.disabled = true;
            sendButton.title = 'MQTT 未连接，无法发送参数设置';
        }
    } else {
        sendButton.disabled = true;
        sendButton.title = 'MQTT 管理器未初始化';
    }
}

/**
 * 初始化参数配置管理器
 * @param {Object} config - 参数配置对象
 */
function initParameterManager(config) {
    console.log('初始化参数配置管理器...');
    parameterManager = new ParameterConfigManager(config);
    window.parameterManager = parameterManager; // 设置为全局变量
}

/**
 * 初始化 MQTT 连接
 */
function initMQTTConnection() {
    console.log('初始化 MQTT 参数配置连接...');
    mqttParameterManager = new MQTTParameterManager();
}

/**
 * 发送参数设置到 MQTT 服务器
 */
async function sendParameterSettings() {
    if (!parameterManager) {
        showStatusMessage('参数管理器未初始化', 'error');
        return;
    }

    if (!mqttParameterManager) {
        showStatusMessage('MQTT 管理器未初始化', 'error');
        return;
    }

    // 检查 MQTT 连接状态
    const connectionStatus = mqttParameterManager.getConnectionStatus();
    if (!connectionStatus.isConnected) {
        showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
        return;
    }

    // 获取需要发送的参数
    const modifiedParams = parameterManager.getModifiedParameters();

    if (modifiedParams.length === 0) {
        showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
        return;
    }

    // 禁用发送按钮
    const sendButton = document.getElementById('send-settings-btn');
    if (sendButton) {
        sendButton.disabled = true;
        sendButton.textContent = '发送中...';
    }

    try {
        // 获取 MQTT 格式的参数数组
        const mqttParams = parameterManager.getMQTTParameterArray();

        // 发送参数设置
        const result = await mqttParameterManager.sendParameterSettings(mqttParams);

        showStatusMessage(
            `参数设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
            'success'
        );

        console.log('参数设置发送成功:', result);

    } catch (error) {
        console.error('发送参数设置失败:', error);
        showStatusMessage(`发送失败: ${error.message}`, 'error');
    } finally {
        // 恢复发送按钮
        if (sendButton) {
            sendButton.disabled = false;
            sendButton.textContent = '下载';
        }
    }
}

/**
 * 显示状态消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型：success, error, warning
 */
function showStatusMessage(message, type = 'success') {
    const statusElement = document.getElementById('status-message');
    if (!statusElement) return;

    statusElement.textContent = message;
    statusElement.className = `status-message ${type}`;
    statusElement.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        statusElement.style.display = 'none';
    }, 3000);
}

/**
 * 通用页面初始化函数
 * @param {Object} config - 参数配置对象
 */
function initParameterConfigPage(config) {
    console.log('参数配置页面初始化...');

    // 初始化参数管理器
    initParameterManager(config);

    // 初始化 MQTT 连接
    initMQTTConnection();

    // 定期更新连接状态显示和按钮状态
    setInterval(() => {
        if (mqttParameterManager) {
            const status = mqttParameterManager.getConnectionStatus();
            if (status.isConnected) {
                updateMQTTStatus('connected', 'MQTT 已连接');
            } else {
                updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
            }
            // 更新发送按钮状态
            updateSendButtonStatus();
        }
    }, 1000);

    console.log('参数配置页面初始化完成');

    // 显示调试功能说明
    console.log('=== 参数配置 MQTT 调试功能 ===');
    console.log('mqttParameterManager.getConnectionStatus() - 获取连接状态');
    console.log('mqttParameterManager.reconnect() - 手动重连');
    console.log('parameterManager.parameters - 查看参数列表');
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (mqttParameterManager) {
        console.log('页面卸载，断开 MQTT 连接');
        mqttParameterManager.disconnect();
    }
});
