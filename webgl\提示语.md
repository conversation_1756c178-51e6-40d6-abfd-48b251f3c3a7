请将 `webgl\main.html` 文件中的内联 JavaScript 代码提取到独立的 JavaScript 文件中，并将这些文件保存到 `webgl\common\` 目录下。具体要求：

1. **提取范围**：提取 `<script>` 标签内的所有 JavaScript 代码（不包括外部引用的脚本）
2. **文件组织**：
   - 按功能模块将代码分割成多个文件
   - 建议的文件命名：
     - `view-switcher.js` - 界面切换功能
     - `modal-manager.js` - 模态框管理功能
     - `unity-commands.js` - Unity命令发送功能
     - `navigation.js` - 导航和模块跳转功能
     - `main-page-init.js` - 主页面初始化功能
3. **目录结构**：在 `webgl\` 目录下创建 `common\` 子目录
4. **更新引用**：修改 `main.html` 文件，移除内联脚本，添加对新创建的 JavaScript 文件的引用
5. **保持功能**：确保提取后的代码功能完全一致，所有函数调用和变量引用正常工作
6. **添加注释**：为每个新文件添加函数级注释，说明文件用途和主要功能

请确保代码提取后，页面的所有交互功能（Unity控制、模态框、导航等）都能正常工作。