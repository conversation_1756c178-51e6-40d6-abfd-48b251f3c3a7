# main.html页面实时报警监控改造说明

## 改造概述

已按照历史事件页面的接口集成方案，对main.html页面中的实时报警监控功能进行了全面改造。

## 主要修改内容

### 1. HTML结构调整

#### 1.1 删除"实时记录"筛选按钮
- 移除了原有的"实时记录"筛选选项
- 保留"所有事件"、"报警事件"、"故障事件"三个筛选选项

#### 1.2 添加状态显示区域
```html
<div class="alarm-status">
  <span class="data-status" id="dataStatus">正在加载...</span>
  <span class="last-update" id="lastUpdate"></span>
</div>
```

### 2. API接口集成

#### 2.1 API配置
```javascript
const ALARM_API_CONFIG = {
  baseUrl: 'https://exdraw.qizhiyun.cc/prod-api',
  endpoints: {
    alertLog: '/iot/alertLog/list'
  },
  headers: {
    'Accept': 'application/json',
    'Authorization': 'Bearer [当前token]'
  }
};
```

#### 2.2 请求参数
- **请求方式**: GET
- **pageNum**: 1（固定第一页）
- **pageSize**: 100（获取前100条数据）
- **不传递时间范围参数**（获取最新数据）

#### 2.3 请求URL格式
```
GET https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=100
```

### 3. 数据处理逻辑

#### 3.1 数据过滤
- 只保留 `alertName: "报警设备"` 和 `alertName: "故障设备"`
- 过滤掉其他类型的事件（如恢复事件）
- 限制显示最多50条记录（可通过配置调整）

#### 3.2 事件信息提取
- 使用 `response.rows[].detail.name` 字段作为事件信息显示
- 自动解析detail字段中的JSON数据
- 处理解析失败的情况

#### 3.3 时间格式化
- 统一时间格式为：`YYYY-MM-DD HH:mm:ss`
- 自动处理多种时间格式输入
- 保持与历史事件页面一致的时间显示

### 4. 页面显示修改

#### 4.1 表格布局调整
- **原布局**: 5列（序号、日期、时间、设备、事件信息）
- **新布局**: 3列（序号、时间、事件信息）
- 删除了设备列，合并了日期和时间列

#### 4.2 CSS样式更新
```css
.alarm-item {
  display: grid;
  grid-template-columns: 50px 180px 1fr;
  gap: 8px;
  /* ... */
}
```

### 5. 新增功能

#### 5.1 实时刷新机制
- 页面加载时立即获取数据
- 每30秒自动刷新一次
- 显示最后更新时间

#### 5.2 状态显示
- **加载中**: 黄色状态，显示"正在加载..."
- **成功**: 绿色状态，显示"数据加载成功"
- **错误**: 红色状态，显示具体错误信息

#### 5.3 错误处理
- CORS错误专门处理
- 认证失败提示（401/403错误）
- 网络错误重试机制
- 用户友好的错误提示

#### 5.4 空状态显示
- 无数据时显示友好的空状态提示
- 错误时提供重新加载按钮

### 6. 核心函数说明

#### 6.1 initRealTimeAlarmMonitor()
- 初始化实时报警监控
- 设置自动刷新定时器
- 立即加载第一次数据

#### 6.2 loadRealTimeAlarmData()
- 从API加载实时报警数据
- 构建GET请求参数
- 处理响应和错误

#### 6.3 processRealTimeAlarmData(data)
- 处理API返回的数据
- 过滤事件类型
- 解析detail字段

#### 6.4 renderRealTimeAlarmList(events)
- 渲染事件列表到DOM
- 应用当前筛选条件
- 生成HTML结构

#### 6.5 formatAlarmDisplayTime(timeString)
- 格式化时间显示
- 统一时间格式
- 处理格式化异常

### 7. 样式改进

#### 7.1 新增状态样式
```css
.data-status.loading { /* 加载状态 */ }
.data-status.success { /* 成功状态 */ }
.data-status.error { /* 错误状态 */ }
.last-update { /* 最后更新时间 */ }
```

#### 7.2 空状态样式
```css
.empty-state { /* 空状态容器 */ }
.retry-btn { /* 重试按钮 */ }
```

#### 7.3 三列布局适配
- 调整grid-template-columns为三列
- 新增alarm-datetime样式
- 保持向后兼容性

### 8. 兼容性处理

#### 8.1 向后兼容
- 保留原有的alarm-date、alarm-time、alarm-device样式
- 支持新旧数据格式混合显示
- 筛选功能保持不变

#### 8.2 错误恢复
- API失败时不影响页面其他功能
- 提供手动重试机制
- 保持原有的模拟数据作为备选

### 9. 性能优化

#### 9.1 数据限制
- 限制显示最多50条记录（可配置）
- 避免DOM过载
- 提高渲染性能

#### 9.2 刷新策略
- 30秒自动刷新间隔（可配置）
- 避免频繁请求
- 可配置的刷新频率

#### 9.3 配置管理
```javascript
const ALARM_DISPLAY_CONFIG = {
  maxDisplayCount: 50, // 最大显示条数
  refreshInterval: 30000 // 刷新间隔（毫秒）
};
```

### 10. 调试支持

#### 10.1 控制台日志
- 详细的API请求日志
- 数据处理过程日志
- 错误信息记录

#### 10.2 状态监控
- 实时显示数据加载状态
- 最后更新时间显示
- 错误状态可视化

## 使用说明

### 启动方式
页面加载后自动启动实时报警监控，无需手动操作。

### 筛选功能
- 点击"所有事件"显示所有报警和故障事件
- 点击"报警事件"只显示报警设备事件
- 点击"故障事件"只显示故障设备事件

### 错误处理
- 如遇CORS错误，联系管理员配置服务器跨域访问
- 如遇认证错误，检查token是否有效
- 点击"重新加载"按钮可手动重试

## 技术特点

1. **完全兼容**: 与历史事件页面使用相同的API和数据处理逻辑
2. **实时更新**: 30秒自动刷新，保持数据实时性
3. **用户友好**: 清晰的状态提示和错误处理
4. **性能优化**: 合理的数据量控制和刷新频率
5. **样式统一**: 保持与整体页面风格一致

## 注意事项

1. **Token更新**: 需要定期更新Authorization token
2. **CORS配置**: 需要服务器端配置允许跨域访问
3. **数据格式**: 依赖API返回数据中的detail.name字段
4. **浏览器兼容**: 使用现代浏览器以获得最佳体验

改造完成后，实时报警监控功能已完全集成真实API，提供了更好的用户体验和数据实时性。

## 改造概述

已成功将main.html页面中的实时报警监控功能从模拟数据改造为使用真实API接口，与历史事件页面保持一致的接口集成方案。

## 主要修改内容

### 1. API接口集成

#### 接口配置
```javascript
const ALARM_API_CONFIG = {
  baseUrl: 'https://exdraw.qizhiyun.cc/prod-api',
  endpoint: '/iot/alertLog/list',
  headers: {
    'Accept': 'application/json',
    'Authorization': 'Bearer [当前token]'
  }
};
```

#### 请求参数
- **请求方式**: GET
- **pageNum**: 1（固定第一页）
- **pageSize**: 100（获取前100条数据）
- **不传递时间范围参数**（获取最新数据）

### 2. 数据处理逻辑

#### 数据筛选
- 只保留 `alertName: "报警设备"` 和 `alertName: "故障设备"`
- 过滤掉其他类型的事件（如恢复事件）
- 使用 `response.rows[].detail.name` 字段作为事件信息显示

#### 数据处理函数
```javascript
function processRealtimeAlarmData(data) {
  // 解析API返回数据
  // 过滤事件类型
  // 解析detail字段获取事件名称
}
```

### 3. 页面显示修改

#### HTML结构调整
- **删除**: "实时记录"筛选按钮
- **保留**: "所有事件"、"报警事件"、"故障事件"筛选
- **新增**: 数据更新时间显示
- **新增**: 数据状态指示器

#### 表格结构变更
- **原有**: 序号 | 日期 | 时间 | 设备 | 事件信息
- **修改后**: 序号 | 时间 | 事件信息
- **时间格式**: 统一为 `YYYY-MM-DD HH:mm:ss`

### 4. 实时刷新功能

#### 自动刷新机制
- **刷新间隔**: 30秒
- **启动时机**: 页面初始化时自动启动
- **停止机制**: 提供停止函数，避免内存泄漏

#### 刷新控制函数
```javascript
function initRealtimeAlarmMonitor() {
  // 立即加载一次数据
  loadRealtimeAlarmData();
  
  // 设置定时刷新
  alarmRefreshInterval = setInterval(loadRealtimeAlarmData, 30000);
}
```

### 5. 用户体验优化

#### 加载状态
- **加载中**: 显示旋转图标和"正在加载..."文字
- **加载成功**: 显示"数据加载成功"状态
- **加载失败**: 显示具体错误信息

#### 错误处理
- **CORS错误**: 专门的跨域错误提示
- **认证错误**: 401/403状态码的专门处理
- **网络错误**: 通用网络错误处理

#### 数据显示
- **空数据**: 显示友好的空状态提示
- **数据限制**: 最多显示15条记录
- **时间显示**: 实时更新数据更新时间

### 6. CSS样式优化

#### 新增样式类
- `.alarm-monitor-section`: 整体容器样式
- `.alarm-header`: 头部区域样式
- `.alarm-status`: 状态显示区域
- `.data-status`: 数据状态指示器
- `.loading-placeholder`: 加载占位符
- `.empty-state`: 空状态显示

#### 响应式设计
- 筛选按钮自适应换行
- 状态信息右对齐显示
- 表格列宽自适应

### 7. 功能对比

#### 修改前（模拟数据）
- ✅ 使用本地模拟数据
- ✅ 定时生成随机事件
- ✅ 包含所有事件类型
- ❌ 数据不真实
- ❌ 无法反映实际系统状态

#### 修改后（真实API）
- ✅ 使用真实API数据
- ✅ 30秒自动刷新
- ✅ 只显示报警和故障事件
- ✅ 数据真实可靠
- ✅ 反映实际系统状态
- ✅ 与历史事件页面数据一致

### 8. 技术实现细节

#### 数据流程
1. **初始化**: 页面加载时启动实时监控
2. **数据请求**: 每30秒发送GET请求到API
3. **数据处理**: 解析响应，过滤事件类型
4. **数据渲染**: 更新DOM显示最新数据
5. **状态更新**: 更新时间和状态指示器

#### 错误恢复
- 网络错误时继续尝试刷新
- 显示错误状态但不停止定时器
- 提供手动重试机制

#### 内存管理
- 及时清理定时器
- 限制显示数据量
- 避免DOM元素累积

### 9. 配置说明

#### Token更新
如需更新认证token，修改以下配置：
```javascript
ALARM_API_CONFIG.headers.Authorization = 'Bearer [NEW_TOKEN]';
```

#### 刷新间隔调整
如需调整刷新间隔，修改以下代码：
```javascript
// 当前30秒，可调整为其他值（毫秒）
alarmRefreshInterval = setInterval(loadRealtimeAlarmData, 30000);
```

#### 显示数量限制
如需调整显示数量，修改以下代码：
```javascript
// 当前限制15条，可调整为其他值
const displayData = filteredAlarmData.slice(0, 15);
```

### 10. 测试验证

#### 功能测试
- ✅ 页面加载时自动开始监控
- ✅ 30秒自动刷新数据
- ✅ 筛选按钮正常工作
- ✅ 时间格式正确显示
- ✅ 错误状态正确提示

#### 兼容性测试
- ✅ 与原有页面功能不冲突
- ✅ 样式与整体设计一致
- ✅ 响应式布局正常

#### 性能测试
- ✅ 内存使用稳定
- ✅ 定时器正确管理
- ✅ DOM更新高效

### 11. 注意事项

#### 服务器要求
- 需要配置CORS允许跨域访问
- 需要支持GET方法访问API接口
- 需要确保认证token有效

#### 浏览器兼容性
- 支持现代浏览器的fetch API
- 支持ES6语法特性
- 支持CSS Grid布局

#### 维护建议
- 定期检查API接口可用性
- 及时更新过期的认证token
- 监控页面性能和内存使用

## 总结

本次改造成功将main.html页面的实时报警监控从模拟数据升级为真实API数据，提高了数据的准确性和实时性，同时保持了良好的用户体验和系统性能。改造后的功能与历史事件页面保持一致，便于维护和管理。
