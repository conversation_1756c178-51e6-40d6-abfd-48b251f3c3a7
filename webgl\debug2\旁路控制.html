<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旁路控制 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <style>
        /* 旁路控制页面专用样式 */
        .control-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
        }

        .unit-count {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .unit-count-value {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 6px;
            padding: 8px 20px;
            color: #00d4ff;
            font-size: 22px;
            font-weight: bold;
        }

        .unit-count-select {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 6px;
            padding: 8px 20px;
            color: #00d4ff;
            font-size: 22px;
            font-weight: bold;
            cursor: pointer;
            outline: none;
            min-height: 45px;
        }

        .unit-count-select option {
            background: rgba(26, 31, 46, 0.95);
            color: #00d4ff;
        }

        .start-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .start-button:hover {
            background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .phase-panels {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .phase-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 20px;
        }

        .phase-title {
            text-align: center;
            color: #00d4ff;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
        }

        .phase-table {
            width: 100%;
            border-collapse: collapse;
        }

        .phase-table th {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            padding: 15px;
            text-align: center;
            color: #00d4ff;
            font-weight: bold;
            font-size: 18px;
        }

        .phase-table td {
            border: 1px solid rgba(0, 212, 255, 0.2);
            padding: 12px;
            text-align: center;
            color: white;
            font-size: 16px;
        }

        .phase-table tr:nth-child(even) {
            background: rgba(0, 212, 255, 0.05);
        }

        .phase-table tr:hover {
            background: rgba(0, 212, 255, 0.1);
        }

        .bypass-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: 1px solid rgba(76, 175, 80, 0.6);
            border-radius: 20px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 80px;
            min-height: 50px;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .bypass-button:hover {
            background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        .bypass-button.active {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
            border-color: #f44336;
            box-shadow: 0 0 15px rgba(244, 67, 54, 0.5);
        }

        .bypass-button.active:hover {
            background: linear-gradient(135deg, #da190b 0%, #c62828 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 18px rgba(244, 67, 54, 0.6);
        }

        .unit-id {
            font-weight: bold;
            color: #00d4ff;
            font-size: 18px;
        }

        .value-cell {
            color: #ccc;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>旁路控制</h1>
        </div>

        <!-- 控制头部 -->
        <div class="control-header">
            <div class="unit-count">
                <span>单元数量：</span>
                <select class="unit-count-select" id="unit-count" onchange="updateUnitCount()">
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                </select>
            </div>
            <div>
                <!-- 旁路控制文本和开始按钮已移除 -->
            </div>
        </div>

        <!-- 三相面板 -->
        <div class="phase-panels">
            <!-- A相 -->
            <div class="phase-panel">
                <div class="phase-title">A相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-a-table">
                        <!-- A相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- B相 -->
            <div class="phase-panel">
                <div class="phase-title">B相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-b-table">
                        <!-- B相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- C相 -->
            <div class="phase-panel">
                <div class="phase-title">C相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-c-table">
                        <!-- C相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <button class="send-button" id="download-btn" onclick="downloadBypassSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <script>
        // 旁路控制页面的 JavaScript 逻辑
        let bypassStates = {
            A: {},
            B: {},
            C: {}
        };

        let mqttClient = null;
        let isConnected = false;
        
        // MQTT 配置
        const mqttConfig = {
            subscribeTopic: '/197/D19EOEN59V1MJ/ws/service',
            publishTopic: '/197/D19EOEN59V1MJ/function/get'
        };

        // 通道状态数据 - 对应SVG_1816, SVG_1817, SVG_1818
        let channelStates = {
            A: 0, // SVG_1816
            B: 0, // SVG_1817  
            C: 0  // SVG_1818
        };

        // 页面加载标志
        let isPageLoaded = false;
        
        // 暂存的修改值
        let pendingChanges = {
            A: null,
            B: null,
            C: null
        };

        /**
         * 初始化MQTT连接
         */
        function initMQTTConnection() {
            console.log('初始化MQTT连接...');
            showStatusMessage('正在连接MQTT服务器...', 'warning');

            const options = {
                username: 'FastBee',
                password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.g1HCISIvQd6YkNgWhblKnXqHeRI74lnP1F8qZOd9XV5a7J41Qi77f9jLxxWd_EVN0XJPP1haYeRK3Uz_xrbEuA',
                cleanSession: true,
                keepAlive: 30,
                clientId: 'web-bypass-' + Math.random().toString(16).substr(2),
                connectTimeout: 60000,
            };

            const brokerUrl = 'wss://mqtt.qizhiyun.cc/mqtt';
            console.log('连接到MQTT服务器:', brokerUrl);

            mqttClient = mqtt.connect(brokerUrl, options);

            mqttClient.on('connect', () => {
                console.log('🔗 MQTT 旁路控制连接成功');
                isConnected = true;
                updateMQTTStatus('connected', 'MQTT 已连接');
                
                // 订阅主题
                mqttClient.subscribe(mqttConfig.subscribeTopic, { qos: 1 }, (err) => {
                    if (!err) {
                        console.log('✅ 订阅主题成功:', mqttConfig.subscribeTopic);
                        showStatusMessage('MQTT连接成功', 'success');
                        // 标记页面已准备好接收初始数据
                        isPageLoaded = false;
                    } else {
                        console.error('❌ 订阅主题失败:', err);
                        showStatusMessage('MQTT订阅失败', 'error');
                    }
                });
            });

            mqttClient.on('message', (topic, message) => {
                console.log('📨 收到MQTT消息:', topic);
                try {
                    const data = JSON.parse(message.toString());
                    console.log('✅ 消息解析成功:', data);
                    handleMQTTMessage(data.message);
                } catch (error) {
                    console.error('❌ 解析MQTT消息失败:', error);
                }
            });

            mqttClient.on('error', (error) => {
                console.error('MQTT连接错误:', error);
                isConnected = false;
                updateMQTTStatus('disconnected', `MQTT连接失败: ${error.message}`);
                showStatusMessage(`MQTT连接失败: ${error.message}`, 'error');
            });

            mqttClient.on('close', () => {
                console.log('MQTT连接已断开');
                isConnected = false;
                updateMQTTStatus('disconnected', 'MQTT连接已断开');
            });
        }

        /**
         * 处理MQTT消息
         */
        function handleMQTTMessage(data) {
            console.log('📥 处理MQTT消息:', data);
            
            // 转换数据格式
            const processedData = {};
            if (Array.isArray(data)) {
                data.forEach(item => {
                    if (item.id !== undefined && item.value !== undefined) {
                        processedData[item.id] = item.value;
                    }
                });
            } else if (typeof data === 'object' && data !== null) {
                Object.assign(processedData, data);
            }
            
            // 页面首次加载时初始化按钮状态，之后只更新当前值
            if (!isPageLoaded) {
                updateChannelStates(processedData);
                isPageLoaded = true;
                console.log('页面初始状态加载完成');
            } else {
                // 实时更新通道状态（当前值）
                updateChannelCurrentValues(processedData);
            }
            
            // 更新数据时间戳
            document.getElementById('data-timestamp').textContent = new Date().toLocaleString();
        }

        /**
         * 更新通道当前值（不影响用户设定的按钮状态）
         */
        function updateChannelCurrentValues(data) {
            // 更新A组通道状态 (SVG_1816)
            if (data['SVG_1816'] !== undefined) {
                channelStates.A = parseInt(data['SVG_1816']);
                updatePhaseCurrentValues('A', channelStates.A);
                console.log('A组通道当前值更新:', channelStates.A);
            }
            
            // 更新B组通道状态 (SVG_1817)
            if (data['SVG_1817'] !== undefined) {
                channelStates.B = parseInt(data['SVG_1817']);
                updatePhaseCurrentValues('B', channelStates.B);
                console.log('B组通道当前值更新:', channelStates.B);
            }
            
            // 更新C组通道状态 (SVG_1818)
            if (data['SVG_1818'] !== undefined) {
                channelStates.C = parseInt(data['SVG_1818']);
                updatePhaseCurrentValues('C', channelStates.C);
                console.log('C组通道当前值更新:', channelStates.C);
            }
        }

        /**
         * 初始化通道状态（页面加载时使用）
         */
        function updateChannelStates(data) {
            // 更新A组通道状态 (SVG_1816)
            if (data['SVG_1816'] !== undefined) {
                channelStates.A = parseInt(data['SVG_1816']);
                updatePhaseFromBitValue('A', channelStates.A);
                console.log('A组通道状态初始化:', channelStates.A);
            }
            
            // 更新B组通道状态 (SVG_1817)
            if (data['SVG_1817'] !== undefined) {
                channelStates.B = parseInt(data['SVG_1817']);
                updatePhaseFromBitValue('B', channelStates.B);
                console.log('B组通道状态初始化:', channelStates.B);
            }
            
            // 更新C组通道状态 (SVG_1818)
            if (data['SVG_1818'] !== undefined) {
                channelStates.C = parseInt(data['SVG_1818']);
                updatePhaseFromBitValue('C', channelStates.C);
                console.log('C组通道状态初始化:', channelStates.C);
            }
        }

        /**
         * 根据位值更新相位当前值显示（不影响按钮状态）
         * @param {string} phase - 相位 (A, B, C)
         * @param {number} bitValue - 位值
         */
        function updatePhaseCurrentValues(phase, bitValue) {
            const unitCount = parseInt(document.getElementById('unit-count').value);
            
            for (let i = 0; i < unitCount; i++) {
                const unitId = `${phase}${(i + 1).toString().padStart(2, '0')}`;
                const bit = (bitValue >> i) & 1; // 获取第i位的值
                const isNormal = bit === 1; // 1表示正常，0表示旁路
                
                // 只更新当前值显示，不改变按钮状态
                const valueCell = document.getElementById(`value-${unitId}`);
                if (valueCell) {
                    // 在当前值列显示实际状态
                    valueCell.textContent = isNormal ? '正常' : '旁路';
                    // 根据实际状态设置颜色
                    valueCell.style.color = isNormal ? '#4CAF50' : '#f44336';
                    valueCell.style.fontWeight = 'bold';
                }
            }
        }

        /**
         * 根据位值更新相位状态（页面初始化时使用）
         * @param {string} phase - 相位 (A, B, C)
         * @param {number} bitValue - 位值
         */
        function updatePhaseFromBitValue(phase, bitValue) {
            const unitCount = parseInt(document.getElementById('unit-count').value);
            
            for (let i = 0; i < unitCount; i++) {
                const unitId = `${phase}${(i + 1).toString().padStart(2, '0')}`;
                const bit = (bitValue >> i) & 1; // 获取第i位的值
                const isNormal = bit === 1; // 1表示正常，0表示旁路
                
                // 更新按钮状态和当前值
                const button = document.getElementById(`btn-${unitId}`);
                const valueCell = document.getElementById(`value-${unitId}`);
                
                if (button && valueCell) {
                    if (isNormal) {
                        button.classList.remove('active');
                        button.textContent = '正常';
                        valueCell.textContent = '正常';
                        valueCell.style.color = '#4CAF50';
                        valueCell.style.fontWeight = 'bold';
                        bypassStates[phase][unitId] = false;
                    } else {
                        button.classList.add('active');
                        button.textContent = '旁路';
                        valueCell.textContent = '旁路';
                        valueCell.style.color = '#f44336';
                        valueCell.style.fontWeight = 'bold';
                        bypassStates[phase][unitId] = true;
                    }
                }
            }
        }

        /**
         * 更新MQTT连接状态
         */
        function updateMQTTStatus(status, message) {
            const mqttStatus = document.getElementById('mqtt-status');
            if (mqttStatus) {
                mqttStatus.textContent = message;
                mqttStatus.className = `mqtt-connection-status ${status}`;
            }
        }

        /**
         * 发送MQTT消息
         */
        function sendMQTTMessage(data) {
            if (!mqttClient || !isConnected) {
                console.error('MQTT客户端未连接');
                showStatusMessage('MQTT连接断开，无法发送消息', 'error');
                return;
            }

            const message = JSON.stringify(data);
              mqttClient.publish(mqttConfig.publishTopic, message, (err) => {
                  if (err) {
                      console.error('发送MQTT消息失败:', err);
                      showStatusMessage(`发送消息失败: ${err.message}`, 'error');
                  } else {
                      console.log('发送MQTT消息成功:', data);
                      showStatusMessage('旁路设置下载成功', 'success');
                  }
              });
        }

        /**
         * 初始化三相表格
         */
        function initPhaseTables() {
            const unitCountSelect = document.getElementById('unit-count');
            const unitCount = parseInt(unitCountSelect.value);
            updatePhaseTables(unitCount);
        }

        /**
         * 更新单元数量
         */
        function updateUnitCount() {
            const unitCountSelect = document.getElementById('unit-count');
            const unitCount = parseInt(unitCountSelect.value);
            updatePhaseTables(unitCount);
            
            // 重新应用当前的通道状态数据
            const currentData = {
                'SVG_1816': channelStates.A,
                'SVG_1817': channelStates.B,
                'SVG_1818': channelStates.C
            };
            updateChannelStates(currentData);
            
            showStatusMessage(`单元数量已更新为: ${unitCount}`, 'success');
        }

        /**
         * 更新三相表格
         */
        function updatePhaseTables(unitCount) {
            const phases = ['A', 'B', 'C'];

            phases.forEach(phase => {
                const tableBody = document.getElementById(`phase-${phase.toLowerCase()}-table`);
                tableBody.innerHTML = '';

                // 重置该相的状态
                bypassStates[phase] = {};

                for (let i = 1; i <= unitCount; i++) {
                    const unitId = `${phase}${i.toString().padStart(2, '0')}`;
                    const row = document.createElement('tr');

                    row.innerHTML = `
                        <td class="unit-id">${unitId}</td>
                        <td>
                            <button class="bypass-button"
                                    id="btn-${unitId}"
                                    onclick="toggleBypass('${phase}', '${unitId}', ${i - 1})">
                                正常
                            </button>
                        </td>
                        <td class="value-cell" id="value-${unitId}">正常</td>
                    `;

                    tableBody.appendChild(row);

                    // 初始化状态为正常
                    bypassStates[phase][unitId] = false;
                }
            });
        }

        /**
         * 切换旁路状态
         * @param {string} phase - 相位
         * @param {string} unitId - 单元ID
         * @param {number} bitIndex - 位索引
         */
        function toggleBypass(phase, unitId, bitIndex) {
            const button = document.getElementById(`btn-${unitId}`);

            // 切换状态
            bypassStates[phase][unitId] = !bypassStates[phase][unitId];

            // 只更新按钮显示，不更新当前值列
            if (bypassStates[phase][unitId]) {
                button.classList.add('active');
                button.textContent = '旁路';
            } else {
                button.classList.remove('active');
                button.textContent = '正常';
            }

            // 计算并暂存当前相位的位和值
            pendingChanges[phase] = calculatePhaseBitValue(phase);

            console.log(`${unitId} 旁路状态:`, bypassStates[phase][unitId]);
            console.log(`${phase}组暂存值:`, pendingChanges[phase]);
            showStatusMessage(`${unitId} ${bypassStates[phase][unitId] ? '已设为旁路' : '已设为正常'} (待下载)`, 'warning');
        }

        /**
         * 计算相位的位和值
         * @param {string} phase - 相位
         * @returns {number} 位和值
         */
        function calculatePhaseBitValue(phase) {
            let bitValue = 0;
            const unitCount = parseInt(document.getElementById('unit-count').value);
            
            for (let i = 0; i < unitCount; i++) {
                const unitId = `${phase}${(i + 1).toString().padStart(2, '0')}`;
                const isNormal = !bypassStates[phase][unitId]; // false表示旁路，true表示正常
                
                if (isNormal) {
                    bitValue |= (1 << i); // 设置第i位为1
                }
            }
            
            return bitValue;
        }

        /**
         * 下载旁路设置
         */
        function downloadBypassSettings() {
            console.log('下载旁路设置');
            
            // 构造MQTT消息，只发送有修改的相位数据
            const mqttData = [];
            const phaseIdMap = {
                'A': 'SVG_1816',
                'B': 'SVG_1817', 
                'C': 'SVG_1818'
            };
            
            let hasChanges = false;
            
            // 检查每个相位是否有修改
            Object.keys(pendingChanges).forEach(phase => {
                if (pendingChanges[phase] !== null) {
                    mqttData.push({
                        id: phaseIdMap[phase],
                        value: pendingChanges[phase].toString()
                    });
                    console.log(`${phase}组位和:`, pendingChanges[phase]);
                    hasChanges = true;
                }
            });
            
            if (!hasChanges) {
                showStatusMessage('没有需要下载的修改', 'warning');
                return;
            }
            
            console.log('发送MQTT数据:', mqttData);
            
            // 发送MQTT消息
            sendMQTTMessage(mqttData);
            
            // 清空暂存的修改
            pendingChanges = {
                A: null,
                B: null,
                C: null
            };
        }

        /**
         * 显示状态消息
         */
        function showStatusMessage(message, type = 'success') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.textContent = message;
            statusDiv.className = `status-message ${type}`;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('旁路控制页面初始化...');

            // 初始化三相表格
            initPhaseTables();

            // 初始化MQTT连接
            initMQTTConnection();

            // 定期更新时间戳
            setInterval(() => {
                if (isConnected) {
                    document.getElementById('data-timestamp').textContent = new Date().toLocaleString();
                }
            }, 1000);
        });
    </script>
</body>
</html>