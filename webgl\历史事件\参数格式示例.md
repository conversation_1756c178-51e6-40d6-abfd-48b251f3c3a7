# 参数格式示例

## 修改说明

根据后台要求，beginTime和endTime参数需要使用 `params[beginTime]` 和 `params[endTime]` 的格式传递。

## 参数结构

### 原始参数对象（JavaScript）
```javascript
const requestParams = {
    pageNum: 1,
    pageSize: 50,
    alertName: "报警设备", // 可选
    params: {
        beginTime: "2025-07-17 00:00:00",
        endTime: "2025-07-24 23:59:59"
    }
};
```

### URL查询参数格式
```
pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17 00:00:00&params[endTime]=2025-07-24 23:59:59
```

### URL编码后的格式
```
pageNum=1&pageSize=50&alertName=%E6%8A%A5%E8%AD%A6%E8%AE%BE%E5%A4%87&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

## 完整的请求示例

### 1. 基本查询（无筛选）
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50
```

### 2. 按事件类型筛选
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备
```

### 3. 按时间范围筛选
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

### 4. 完整筛选（事件类型 + 时间范围）
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

## JavaScript实现

### 参数构建代码
```javascript
// 构建请求参数
const params = {
    pageNum: currentPage,
    pageSize: pageSize
};

// 添加事件类型筛选
if (currentFilter.type !== 'all') {
    params.alertName = currentFilter.type;
}

// 添加时间范围筛选
if (currentFilter.startDate || currentFilter.endDate) {
    params.params = {};
    if (currentFilter.startDate) {
        params.params.beginTime = formatDateTimeForAPI(new Date(currentFilter.startDate));
    }
    if (currentFilter.endDate) {
        params.params.endTime = formatDateTimeForAPI(new Date(currentFilter.endDate));
    }
}

// 构建URL参数
const urlParams = new URLSearchParams();
urlParams.append('pageNum', params.pageNum);
urlParams.append('pageSize', params.pageSize);

if (params.alertName) {
    urlParams.append('alertName', params.alertName);
}

if (params.params) {
    // 使用params[beginTime]和params[endTime]格式
    if (params.params.beginTime) {
        urlParams.append('params[beginTime]', params.params.beginTime);
    }
    if (params.params.endTime) {
        urlParams.append('params[endTime]', params.params.endTime);
    }
}

// 生成最终URL
const apiUrl = API_CONFIG.baseUrl + API_CONFIG.endpoints.alertLog + '?' + urlParams.toString();
```

## 后台解析说明

后台需要按以下方式解析参数：

### Java Spring Boot示例
```java
@GetMapping("/iot/alertLog/list")
public ResponseEntity<?> getAlertLogList(
    @RequestParam int pageNum,
    @RequestParam int pageSize,
    @RequestParam(required = false) String alertName,
    @RequestParam(required = false) @RequestParam("params[beginTime]") String beginTime,
    @RequestParam(required = false) @RequestParam("params[endTime]") String endTime
) {
    // 直接使用beginTime和endTime参数
    if (beginTime != null && endTime != null) {
        // 使用beginTime和endTime进行查询
    }

    // 执行查询逻辑
    return ResponseEntity.ok(result);
}
```

### 或者使用Map接收params参数
```java
@GetMapping("/iot/alertLog/list")
public ResponseEntity<?> getAlertLogList(
    @RequestParam int pageNum,
    @RequestParam int pageSize,
    @RequestParam(required = false) String alertName,
    @RequestParam(required = false) Map<String, String> params
) {
    // 从params Map中获取时间参数
    if (params != null) {
        String beginTime = params.get("beginTime");
        String endTime = params.get("endTime");
        // 使用beginTime和endTime进行查询
    }

    // 执行查询逻辑
    return ResponseEntity.ok(result);
}
```

### Node.js Express示例
```javascript
app.get('/iot/alertLog/list', (req, res) => {
    const { pageNum, pageSize, alertName } = req.query;
    const beginTime = req.query['params[beginTime]'];
    const endTime = req.query['params[endTime]'];

    // 直接使用beginTime和endTime
    if (beginTime && endTime) {
        // 使用beginTime和endTime进行查询
    }

    // 执行查询逻辑
    // ...
});
```

## 测试验证

### 1. 控制台输出示例
```
GET请求URL: https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59

请求参数: {
  pageNum: 1,
  pageSize: 50,
  alertName: "报警设备",
  params: {
    beginTime: "2025-07-17 00:00:00",
    endTime: "2025-07-24 23:59:59"
  }
}
```

### 2. 网络请求检查
在浏览器开发者工具的Network标签页中，可以看到：
- **Request URL**: 包含完整的查询参数
- **Query String Parameters**:
  - `pageNum`: 1
  - `pageSize`: 50
  - `alertName`: 报警设备
  - `params[beginTime]`: 2025-07-17 00:00:00
  - `params[endTime]`: 2025-07-24 23:59:59

## 注意事项

### 1. 数组参数格式
- 使用 `params[beginTime]` 和 `params[endTime]` 格式
- 后台可以直接接收为独立参数或Map格式

### 2. 时间格式
- 时间格式统一为：`YYYY-MM-DD HH:mm:ss`
- 确保前后端时间格式一致

### 3. 可选参数
- `alertName`、`params[beginTime]`和`params[endTime]`都是可选参数
- 不传递时表示不进行相应的筛选

### 4. 错误处理
- 后台需要处理参数格式错误
- 前端需要处理URL构建错误

## 兼容性说明

这种参数格式：
- ✅ 保持了原有的参数结构
- ✅ 符合GET请求的规范
- ✅ 便于后台解析和处理
- ✅ 支持复杂的筛选条件

修改后的代码已经完全适配这种参数格式，可以正常工作。
