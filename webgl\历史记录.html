<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 历史记录</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 历史记录页面专用样式
         * 基于项目统一的科技蓝色主题设计
         * 实现类似状态机的状态切换分析功能
         */
        .history-records-container {
            width: 1366px;
            height: 768px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .history-records-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .history-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: var(--shadow-secondary);
            position: relative;
            z-index: 100;
        }

        .history-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--primary-color) 25%,
                var(--accent-color) 50%,
                var(--primary-color) 75%,
                transparent 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .history-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .history-title h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .history-title i {
            font-size: 32px;
            color: var(--primary-color);
            animation: logoSpin 4s linear infinite;
        }

        .history-time {
            font-family: var(--font-mono);
            font-size: 16px;
            font-weight: 600;
            color: var(--accent-color);
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
        }

        /* 主内容区域 */
        .history-main {
            height: calc(768px - 80px);
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 20px;
            padding: 20px;
        }

        /* 左侧控制面板 */
        .control-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .control-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .control-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .control-section {
            margin-bottom: 25px;
        }

        .control-section h4 {
            font-size: 14px;
            color: var(--primary-color);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            background: linear-gradient(90deg, rgba(0, 212, 255, 0.12), rgba(0, 153, 204, 0.08), transparent);
            padding: 6px 10px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }

        /* 日期时间选择器 */
        .datetime-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .datetime-input {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: var(--font-mono);
        }

        .datetime-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        /* 筛选按钮 */
        .filter-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-btn {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-secondary);
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
            color: var(--bg-primary);
            font-weight: 600;
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--bg-primary);
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
        }

        .action-btn.secondary {
            background: rgba(0, 212, 255, 0.1);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .action-btn.secondary:hover {
            background: rgba(0, 212, 255, 0.2);
        }

        /* 右侧记录显示区域 */
        .records-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .records-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .records-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .records-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-count {
            font-weight: bold;
            color: var(--primary-color);
            font-family: var(--font-mono);
        }

        /* 记录表格容器 */
        .records-table-container {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 表头样式 */
        .records-table-header {
            display: grid;
            grid-template-columns: 50px 140px 80px 100px 1fr 80px 60px 80px;
            gap: 8px;
            padding: 12px 20px;
            background: rgba(0, 212, 255, 0.1);
            border-bottom: 1px solid var(--border-color);
            font-size: 13px;
            font-weight: 600;
            color: var(--primary-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .header-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .header-cell.serial {
            justify-content: flex-end;
            padding-right: 8px;
        }

        .header-cell.datetime {
            justify-content: flex-start;
        }

        /* 表格内容 */
        .records-table-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .record-item {
            display: grid;
            grid-template-columns: 50px 140px 80px 100px 1fr 80px 60px 80px;
            gap: 8px;
            padding: 10px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            align-items: center;
            font-size: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .record-item:hover {
            background: rgba(0, 212, 255, 0.05);
            border-left: 3px solid var(--primary-color);
            padding-left: 17px;
        }

        .record-serial {
            text-align: right;
            font-weight: bold;
            font-family: var(--font-mono);
            padding-right: 8px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        .record-datetime {
            font-family: var(--font-mono);
            font-size: 11px;
            color: var(--text-primary);
            text-align: left;
        }

        .record-type {
            text-align: center;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
        }

        .record-type.button {
            background: rgba(0, 212, 255, 0.2);
            color: var(--primary-color);
        }

        .record-type.system {
            background: rgba(102, 224, 255, 0.2);
            color: var(--accent-color);
        }

        .record-type.io {
            background: rgba(0, 255, 136, 0.2);
            color: var(--success-color);
        }

        .record-type.dsp {
            background: rgba(255, 170, 0, 0.2);
            color: var(--warning-color);
        }

        .record-type.power {
            background: rgba(255, 68, 68, 0.2);
            color: var(--error-color);
        }

        .record-type.control {
            background: rgba(153, 102, 255, 0.2);
            color: #9966ff;
        }

        .record-device {
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
        }

        .record-action {
            color: var(--text-secondary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .record-value {
            font-family: var(--font-mono);
            font-weight: 600;
            color: var(--primary-color);
            text-align: right;
        }

        .record-unit {
            font-size: 10px;
            color: var(--text-muted);
            text-align: center;
        }

        .record-status {
            text-align: center;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }

        .record-status.normal {
            background: rgba(0, 255, 136, 0.2);
            color: var(--success-color);
        }

        .record-status.warning {
            background: rgba(255, 170, 0, 0.2);
            color: var(--warning-color);
        }

        .record-status.error {
            background: rgba(255, 68, 68, 0.2);
            color: var(--error-color);
        }

        .record-status.offline {
            background: rgba(122, 139, 160, 0.2);
            color: var(--text-muted);
        }

        /* 分页控制 */
        .pagination-container {
            padding: 15px 20px;
            background: rgba(0, 212, 255, 0.05);
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: var(--font-mono);
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: var(--primary-color);
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            min-width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-btn:hover:not(:disabled) {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-info {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: var(--font-mono);
            margin: 0 10px;
        }

        /* 空状态显示 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state p {
            font-size: 16px;
            margin: 0;
        }

        /* 滚动条样式优化 */
        .control-content::-webkit-scrollbar,
        .records-table-content::-webkit-scrollbar {
            width: 6px;
        }

        .control-content::-webkit-scrollbar-track,
        .records-table-content::-webkit-scrollbar-track {
            background: rgba(0, 212, 255, 0.1);
            border-radius: 3px;
        }

        .control-content::-webkit-scrollbar-thumb,
        .records-table-content::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
        }

        .control-content::-webkit-scrollbar-thumb:hover,
        .records-table-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, var(--accent-color), var(--primary-color));
        }

        /* 响应式优化 */
        @media (max-width: 1400px) {
            .history-records-container {
                width: 100%;
                max-width: 1366px;
            }

            .history-main {
                grid-template-columns: 300px 1fr;
                gap: 15px;
                padding: 15px;
            }
        }

        @media (max-width: 1200px) {
            .history-main {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }

            .control-panel {
                height: 250px;
            }

            .records-table-header,
            .record-item {
                grid-template-columns: 40px 120px 70px 90px 1fr 70px 50px 70px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="history-records-container">
        <!-- 顶部标题栏 -->
        <header class="history-header" style="display:none">
            <div class="history-title">
                <i class="fas fa-database"></i>
                <h1>历史记录</h1>
            </div>
            <div class="history-time" id="currentTime">
                2025-01-01 00:00:00
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="history-main">
            <!-- 左侧控制面板 -->
            <section class="control-panel">
                <div class="control-header">
                    <h3><i class="fas fa-cogs"></i>查询控制</h3>
                </div>
                <div class="control-content">
                    <!-- 时间范围选择 -->
                    <div class="control-section">
                        <h4><i class="fas fa-clock"></i>时间范围</h4>
                        <div class="datetime-selector">
                            <input type="datetime-local" class="datetime-input" id="startDateTime" title="开始时间">
                            <input type="datetime-local" class="datetime-input" id="endDateTime" title="结束时间">
                        </div>
                        <div class="filter-buttons">
                            <button class="filter-btn" onclick="setTimeRange('today')">
                                <i class="fas fa-calendar-day"></i>
                                <span>今天</span>
                            </button>
                            <button class="filter-btn" onclick="setTimeRange('week')">
                                <i class="fas fa-calendar-week"></i>
                                <span>最近一周</span>
                            </button>
                            <button class="filter-btn" onclick="setTimeRange('month')">
                                <i class="fas fa-calendar-alt"></i>
                                <span>最近一月</span>
                            </button>
                        </div>
                    </div>

                    <!-- 记录类型筛选 -->
                    <div class="control-section">
                        <h4><i class="fas fa-tags"></i>记录类型</h4>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-type="all">
                                <i class="fas fa-list"></i>
                                <span>所有记录</span>
                            </button>
                            <button class="filter-btn" data-type="button">
                                <i class="fas fa-hand-pointer"></i>
                                <span>按钮动作</span>
                            </button>
                            <button class="filter-btn" data-type="system">
                                <i class="fas fa-microchip"></i>
                                <span>系统动作</span>
                            </button>
                            <button class="filter-btn" data-type="io">
                                <i class="fas fa-exchange-alt"></i>
                                <span>I/O状态</span>
                            </button>
                            <button class="filter-btn" data-type="dsp">
                                <i class="fas fa-cpu"></i>
                                <span>DSP状态</span>
                            </button>
                            <button class="filter-btn" data-type="power">
                                <i class="fas fa-bolt"></i>
                                <span>电压电流</span>
                            </button>
                            <button class="filter-btn" data-type="control">
                                <i class="fas fa-sliders-h"></i>
                                <span>控制动作</span>
                            </button>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="control-section">
                        <h4><i class="fas fa-tools"></i>操作功能</h4>
                        <div class="action-buttons">
                            <button class="action-btn" onclick="queryRecords()">
                                <i class="fas fa-search"></i>
                                <span>查询记录</span>
                            </button>
                            <button class="action-btn secondary" onclick="exportRecords()">
                                <i class="fas fa-download"></i>
                                <span>导出数据</span>
                            </button>
                            <button class="action-btn secondary" onclick="clearFilters()">
                                <i class="fas fa-refresh"></i>
                                <span>重置筛选</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右侧记录显示区域 -->
            <section class="records-panel">
                <div class="records-header">
                    <h3><i class="fas fa-table"></i>历史记录数据</h3>
                    <div class="records-stats">
                        <div class="stat-item">
                            <i class="fas fa-list"></i>
                            <span>总计: <span class="stat-count" id="totalRecords">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-hand-pointer" style="color: var(--primary-color);"></i>
                            <span>按钮: <span class="stat-count" id="buttonRecords">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-microchip" style="color: var(--accent-color);"></i>
                            <span>系统: <span class="stat-count" id="systemRecords">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-exchange-alt" style="color: var(--success-color);"></i>
                            <span>I/O: <span class="stat-count" id="ioRecords">0</span></span>
                        </div>
                    </div>
                </div>

                <!-- 记录表格 -->
                <div class="records-table-container">
                    <!-- 表头 -->
                    <div class="records-table-header">
                        <div class="header-cell serial">序号</div>
                        <div class="header-cell datetime">时间</div>
                        <div class="header-cell type">类型</div>
                        <div class="header-cell device">设备/模块</div>
                        <div class="header-cell action">动作/状态</div>
                        <div class="header-cell value">数值</div>
                        <div class="header-cell unit">单位</div>
                        <div class="header-cell status">状态</div>
                    </div>

                    <!-- 表格内容 -->
                    <div class="records-table-content" id="recordsTableContent">
                        <!-- 记录项将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 分页控制 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span>显示第 <span id="currentPageStart">1</span> - <span id="currentPageEnd">50</span> 条，共 <span id="totalCount">0</span> 条记录</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="page-btn" id="firstPageBtn" onclick="goToPage(1)">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="page-btn" id="prevPageBtn" onclick="goToPage(currentPage - 1)">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="page-info">第 <span id="currentPageNum">1</span> 页，共 <span id="totalPages">1</span> 页</span>
                        <button class="page-btn" id="nextPageBtn" onclick="goToPage(currentPage + 1)">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="page-btn" id="lastPageBtn" onclick="goToPage(totalPages)">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 历史记录页面脚本
         * 处理历史记录数据展示、筛选、分页和导出功能
         * 实现类似状态机的状态切换分析功能
         */

        // 全局变量
        let allRecords = []; // 存储所有历史记录
        let filteredRecords = []; // 存储筛选后的记录
        let currentPage = 1; // 当前页码
        let pageSize = 50; // 每页显示记录数
        let totalPages = 1; // 总页数
        let currentFilter = {
            type: 'all',
            startTime: null,
            endTime: null
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('历史记录页面初始化开始...');
            initHistoryRecordsPage();
            updateTime();
            setInterval(updateTime, 1000);
            console.log('历史记录页面初始化完成');
        });

        /**
         * 初始化历史记录页面
         */
        function initHistoryRecordsPage() {
            initFilterButtons();
            setDefaultTimeRange();
            generateHistoryRecords();
            queryRecords();
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 初始化筛选按钮
         */
        function initFilterButtons() {
            const typeButtons = document.querySelectorAll('[data-type]');
            typeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除其他按钮的active状态
                    typeButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加当前按钮的active状态
                    this.classList.add('active');
                    // 更新筛选条件
                    currentFilter.type = this.dataset.type;
                });
            });
        }

        /**
         * 设置默认时间范围（最近一周）
         */
        function setDefaultTimeRange() {
            const now = new Date();
            const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

            document.getElementById('startDateTime').value = formatDateTimeLocal(oneWeekAgo);
            document.getElementById('endDateTime').value = formatDateTimeLocal(now);
        }

        /**
         * 设置时间范围
         * @param {string} range - 时间范围类型
         */
        function setTimeRange(range) {
            const now = new Date();
            let startTime;

            switch(range) {
                case 'today':
                    startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'week':
                    startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    return;
            }

            document.getElementById('startDateTime').value = formatDateTimeLocal(startTime);
            document.getElementById('endDateTime').value = formatDateTimeLocal(now);
        }

        /**
         * 格式化日期时间为本地输入格式
         * @param {Date} date - 日期对象
         * @returns {string} 格式化的日期时间字符串
         */
        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        /**
         * 生成历史记录数据（模拟数据）
         * 创建涵盖各种系统动作和按钮操作的历史记录
         */
        function generateHistoryRecords() {
            const recordTypes = [
                { type: 'button', name: '按钮动作' },
                { type: 'system', name: '系统动作' },
                { type: 'io', name: 'I/O状态' },
                { type: 'dsp', name: 'DSP状态' },
                { type: 'power', name: '电压电流' },
                { type: 'control', name: '控制动作' }
            ];

            const devices = [
                'SVG主控制器', 'IGBT模块A', 'IGBT模块B', 'IGBT模块C',
                '直流母线', '交流接触器', '电压传感器', '电流传感器',
                'DSP处理器', '通信模块', '冷却系统', '保护继电器',
                '功率计量', '谐波分析', '无功补偿', '电能质量'
            ];

            const buttonActions = [
                '启动按钮', '停止按钮', '复位按钮', '确认按钮',
                '手动模式', '自动模式', '本地控制', '远程控制',
                '参数设置', '状态查询', '故障复位', '系统自检'
            ];

            const systemActions = [
                '系统启动', '系统停止', '模式切换', '参数更新',
                '状态监测', '故障检测', '保护动作', '恢复运行',
                '数据采集', '通信检测', '自检完成', '配置加载'
            ];

            const ioStates = [
                '输入状态变化', '输出状态变化', '开关量输入', '开关量输出',
                '模拟量输入', '模拟量输出', '数字量处理', '信号转换',
                '接点状态', '继电器动作', '传感器信号', '执行器控制'
            ];

            const dspStates = [
                'DSP初始化', 'DSP运行', 'DSP停止', 'DSP故障',
                '算法执行', '数据处理', '控制计算', '保护算法',
                '通信处理', '中断响应', '定时任务', '状态更新'
            ];

            const powerActions = [
                '电压测量', '电流测量', '功率计算', '频率检测',
                '相位测量', '谐波分析', '功率因数', '电能计量',
                '电压调节', '电流控制', '无功补偿', '有功调节'
            ];

            const controlActions = [
                '充电控制', '放电控制', '模式切换', '参数调节',
                '保护投入', '保护退出', '联锁检查', '闭锁控制',
                '远程控制', '本地控制', '自动控制', '手动控制'
            ];

            const statusList = ['normal', 'warning', 'error', 'offline'];
            const statusNames = {
                'normal': '正常',
                'warning': '告警',
                'error': '故障',
                'offline': '离线'
            };

            // 生成最近30天的历史记录
            const now = new Date();
            allRecords = [];

            for (let i = 0; i < 500; i++) {
                // 随机生成过去30天内的时间
                const randomDays = Math.random() * 30;
                const randomHours = Math.random() * 24;
                const randomMinutes = Math.random() * 60;
                const randomSeconds = Math.random() * 60;
                const recordTime = new Date(now.getTime() -
                    (randomDays * 24 * 60 * 60 * 1000) -
                    (randomHours * 60 * 60 * 1000) -
                    (randomMinutes * 60 * 1000) -
                    (randomSeconds * 1000));

                const recordType = recordTypes[Math.floor(Math.random() * recordTypes.length)];
                const device = devices[Math.floor(Math.random() * devices.length)];
                const status = statusList[Math.floor(Math.random() * statusList.length)];

                let action, value, unit;

                switch(recordType.type) {
                    case 'button':
                        action = buttonActions[Math.floor(Math.random() * buttonActions.length)];
                        value = '';
                        unit = '';
                        break;
                    case 'system':
                        action = systemActions[Math.floor(Math.random() * systemActions.length)];
                        value = '';
                        unit = '';
                        break;
                    case 'io':
                        action = ioStates[Math.floor(Math.random() * ioStates.length)];
                        value = Math.random() > 0.5 ? '1' : '0';
                        unit = '';
                        break;
                    case 'dsp':
                        action = dspStates[Math.floor(Math.random() * dspStates.length)];
                        value = (Math.random() * 100).toFixed(1);
                        unit = '%';
                        break;
                    case 'power':
                        action = powerActions[Math.floor(Math.random() * powerActions.length)];
                        if (action.includes('电压')) {
                            value = (220 + Math.random() * 20 - 10).toFixed(1);
                            unit = 'V';
                        } else if (action.includes('电流')) {
                            value = (Math.random() * 100).toFixed(2);
                            unit = 'A';
                        } else if (action.includes('功率')) {
                            value = (Math.random() * 1000).toFixed(1);
                            unit = 'kW';
                        } else {
                            value = (Math.random() * 50).toFixed(1);
                            unit = 'Hz';
                        }
                        break;
                    case 'control':
                        action = controlActions[Math.floor(Math.random() * controlActions.length)];
                        value = (Math.random() * 100).toFixed(1);
                        unit = '%';
                        break;
                }

                allRecords.push({
                    id: i + 1,
                    type: recordType.type,
                    typeName: recordType.name,
                    device: device,
                    action: action,
                    value: value,
                    unit: unit,
                    status: status,
                    statusName: statusNames[status],
                    timestamp: recordTime.getTime(),
                    datetime: formatDateTime(recordTime)
                });
            }

            // 按时间戳降序排序（最新的在前）
            allRecords.sort((a, b) => b.timestamp - a.timestamp);

            console.log(`生成了 ${allRecords.length} 条历史记录`);
        }

        /**
         * 格式化日期时间显示
         * @param {Date} date - 日期对象
         * @returns {string} 格式化的日期时间字符串
         */
        function formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        /**
         * 查询记录
         */
        function queryRecords() {
            // 获取时间范围
            const startDateTimeInput = document.getElementById('startDateTime');
            const endDateTimeInput = document.getElementById('endDateTime');

            if (startDateTimeInput.value) {
                currentFilter.startTime = new Date(startDateTimeInput.value).getTime();
            } else {
                currentFilter.startTime = null;
            }

            if (endDateTimeInput.value) {
                currentFilter.endTime = new Date(endDateTimeInput.value).getTime();
            } else {
                currentFilter.endTime = null;
            }

            // 应用筛选
            applyFilters();
        }

        /**
         * 应用筛选条件
         */
        function applyFilters() {
            filteredRecords = allRecords.filter(record => {
                // 记录类型筛选
                if (currentFilter.type !== 'all' && record.type !== currentFilter.type) {
                    return false;
                }

                // 时间范围筛选
                if (currentFilter.startTime && record.timestamp < currentFilter.startTime) {
                    return false;
                }
                if (currentFilter.endTime && record.timestamp > currentFilter.endTime) {
                    return false;
                }

                return true;
            });

            // 重置到第一页
            currentPage = 1;
            updatePagination();
            renderRecordsList();
            updateStatistics();
        }

        /**
         * 更新分页信息
         */
        function updatePagination() {
            totalPages = Math.ceil(filteredRecords.length / pageSize);
            if (totalPages === 0) totalPages = 1;

            // 确保当前页在有效范围内
            if (currentPage > totalPages) currentPage = totalPages;
            if (currentPage < 1) currentPage = 1;

            // 更新分页显示
            document.getElementById('currentPageNum').textContent = currentPage;
            document.getElementById('totalPages').textContent = totalPages;
            document.getElementById('totalCount').textContent = filteredRecords.length;

            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredRecords.length);
            document.getElementById('currentPageStart').textContent = filteredRecords.length > 0 ? startIndex : 0;
            document.getElementById('currentPageEnd').textContent = filteredRecords.length > 0 ? endIndex : 0;

            // 更新按钮状态
            document.getElementById('firstPageBtn').disabled = currentPage === 1;
            document.getElementById('prevPageBtn').disabled = currentPage === 1;
            document.getElementById('nextPageBtn').disabled = currentPage === totalPages;
            document.getElementById('lastPageBtn').disabled = currentPage === totalPages;
        }

        /**
         * 跳转到指定页面
         * @param {number} page - 页码
         */
        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            currentPage = page;
            updatePagination();
            renderRecordsList();
        }

        /**
         * 渲染记录列表
         */
        function renderRecordsList() {
            const recordsContent = document.getElementById('recordsTableContent');

            if (filteredRecords.length === 0) {
                recordsContent.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>暂无符合条件的历史记录</p>
                    </div>
                `;
                return;
            }

            // 计算当前页的记录
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, filteredRecords.length);
            const pageRecords = filteredRecords.slice(startIndex, endIndex);

            const recordsHtml = pageRecords.map((record, index) => `
                <div class="record-item" data-record-id="${record.id}">
                    <span class="record-serial">${startIndex + index + 1}</span>
                    <span class="record-datetime">${record.datetime}</span>
                    <span class="record-type ${record.type}">${record.typeName}</span>
                    <span class="record-device">${record.device}</span>
                    <span class="record-action" title="${record.action}">${record.action}</span>
                    <span class="record-value">${record.value}</span>
                    <span class="record-unit">${record.unit}</span>
                    <span class="record-status ${record.status}">${record.statusName}</span>
                </div>
            `).join('');

            recordsContent.innerHTML = recordsHtml;

            // 添加点击事件监听
            const recordItems = recordsContent.querySelectorAll('.record-item');
            recordItems.forEach(item => {
                item.addEventListener('click', function() {
                    const recordId = parseInt(this.dataset.recordId);
                    showRecordDetails(recordId);
                });
            });
        }

        /**
         * 更新统计信息
         */
        function updateStatistics() {
            const totalRecords = filteredRecords.length;
            const buttonRecords = filteredRecords.filter(record => record.type === 'button').length;
            const systemRecords = filteredRecords.filter(record => record.type === 'system').length;
            const ioRecords = filteredRecords.filter(record => record.type === 'io').length;

            document.getElementById('totalRecords').textContent = totalRecords;
            document.getElementById('buttonRecords').textContent = buttonRecords;
            document.getElementById('systemRecords').textContent = systemRecords;
            document.getElementById('ioRecords').textContent = ioRecords;
        }

        /**
         * 显示记录详情
         * @param {number} recordId - 记录ID
         */
        function showRecordDetails(recordId) {
            const record = allRecords.find(r => r.id === recordId);
            if (record) {
                console.log('显示记录详情:', record);
                const detailInfo = `记录详情:
设备: ${record.device}
类型: ${record.typeName}
时间: ${record.datetime}
动作: ${record.action}
数值: ${record.value} ${record.unit}
状态: ${record.statusName}`;
                alert(detailInfo);
            }
        }

        /**
         * 导出记录数据
         */
        function exportRecords() {
            if (filteredRecords.length === 0) {
                alert('没有可导出的记录数据');
                return;
            }

            try {
                // 创建CSV内容
                const headers = ['序号', '时间', '类型', '设备/模块', '动作/状态', '数值', '单位', '状态'];
                const csvContent = [
                    headers.join(','),
                    ...filteredRecords.map((record, index) => [
                        index + 1,
                        `"${record.datetime}"`,
                        `"${record.typeName}"`,
                        `"${record.device}"`,
                        `"${record.action}"`,
                        record.value,
                        record.unit,
                        `"${record.statusName}"`
                    ].join(','))
                ].join('\n');

                // 添加BOM以支持中文
                const BOM = '\uFEFF';
                const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

                // 创建下载链接
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);

                // 生成文件名
                const now = new Date();
                const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
                link.setAttribute('download', `历史记录_${timestamp}.csv`);

                // 触发下载
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log(`导出了 ${filteredRecords.length} 条历史记录`);
                alert(`成功导出 ${filteredRecords.length} 条历史记录`);

            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败，请重试');
            }
        }

        /**
         * 清除筛选条件
         */
        function clearFilters() {
            // 重置筛选条件
            currentFilter = {
                type: 'all',
                startTime: null,
                endTime: null
            };

            // 重置UI状态
            const typeButtons = document.querySelectorAll('[data-type]');
            typeButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelector('[data-type="all"]').classList.add('active');

            // 重置时间范围
            setDefaultTimeRange();

            // 重新查询
            queryRecords();

            console.log('已清除所有筛选条件');
        }

        /**
         * 获取记录类型中文名称
         * @param {string} type - 记录类型
         * @returns {string} 中文名称
         */
        function getRecordTypeName(type) {
            const typeNames = {
                'button': '按钮动作',
                'system': '系统动作',
                'io': 'I/O状态',
                'dsp': 'DSP状态',
                'power': '电压电流',
                'control': '控制动作'
            };
            return typeNames[type] || type;
        }

        /**
         * 获取状态中文名称
         * @param {string} status - 状态
         * @returns {string} 中文名称
         */
        function getStatusName(status) {
            const statusNames = {
                'normal': '正常',
                'warning': '告警',
                'error': '故障',
                'offline': '离线'
            };
            return statusNames[status] || status;
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            // Ctrl+Enter 快速查询
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                queryRecords();
            }

            // Ctrl+E 导出数据
            if (event.ctrlKey && event.key === 'e') {
                event.preventDefault();
                exportRecords();
            }

            // Ctrl+R 重置筛选
            if (event.ctrlKey && event.key === 'r') {
                event.preventDefault();
                clearFilters();
            }

            // 分页快捷键
            if (event.key === 'ArrowLeft' && event.ctrlKey) {
                event.preventDefault();
                goToPage(currentPage - 1);
            }

            if (event.key === 'ArrowRight' && event.ctrlKey) {
                event.preventDefault();
                goToPage(currentPage + 1);
            }
        });
    </script>
</body>
</html>
