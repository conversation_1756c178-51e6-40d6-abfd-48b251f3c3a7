# 单元状态数据二进制位解析功能说明

## 功能概述

针对 `参数曲线.html` 页面实现了单元状态数据的二进制位解析功能。当用户在设备选择下拉菜单中选择"单元状态"时，系统会自动对返回的数据进行预处理，将以"单元信息"开头的参数的16位二进制数据展开为16个具体的布尔状态参数。

## 实现的核心功能

### 1. 参数模型展开 (`expandUnitStatusParameters`)

**功能描述：**
- 检测参数名称是否以"单元信息"开头
- 将单个16位二进制数据参数拆分为16个独立的布尔状态参数
- 每个拆分后的参数对应原始数据的一个二进制位（从最高位到最低位）

**参数映射规则（以"单元信息A1"为例）：**
```
原始参数："单元信息A1" → 16位二进制数据

拆分后的16个参数（从最高位开始）：
第1位（最高位） → "A1下行光纤断"
第2位 → "A1封锁"
第3位 → "A1停止"
第4位 → "A1启动"
第5位 → "A1电源故障"
第6位 → "A1上行光纤断"
第7位 → "A1下行光纤丢同步"
第8位 → "A1下行光纤无光"
第9位 → "A1过压报警"
第10位 → "A1超温"
第11位 → "A1欠压"
第12位 → "A1过压"
第13位 → "A1过流4"
第14位 → "A1过流3"
第15位 → "A1过流2"
第16位（最低位） → "A1过流1"
```

**data-identifier 处理：**
- 原始标识符：HMI_30490
- 新标识符格式：HMI_30490_0, HMI_30490_1, ..., HMI_30490_15
- 每个新标识符对应相应二进制位的状态值

### 2. 数据值展开 (`expandUnitStatusValues`)

**功能描述：**
- 在数据获取后、图表渲染前进行预处理
- 将16位二进制数据值拆分为16个独立的0/1布尔值
- 使用位运算 `(value >> (15-i)) & 1` 获取第i+1位的值

**处理流程：**
1. 检测数据值是否对应"单元信息"参数
2. 将数值转换为16位二进制表示
3. 通过位运算提取每一位的值
4. 为每个位创建新的数据值对象

### 3. 界面集成

**参数控制面板：**
- 自动检测"单元状态"设备类型
- 显示展开后的具体状态参数名称
- 默认启用所有展开后的参数
- 支持参数搜索和选择

**图表显示：**
- 每个状态参数以独立曲线形式展示
- 值严格为0或1的数字信号
- 支持参数开关控制显示/隐藏
- 保持与现有界面样式一致

## 技术实现要点

### 1. 设备类型检测
```javascript
const isUnitStatusDevice = currentDevice && currentDevice.deviceName === '单元状态';
```

### 2. 参数名称匹配
```javascript
param.modelName && param.modelName.startsWith('单元信息')
```

### 3. 二进制位提取
```javascript
const bitValue = (numValue >> (15 - i)) & 1;
```

### 4. 标识符生成
```javascript
identifier: `${param.identifier}_${i}` // 原标识符_位序号
```

## 使用说明

1. **选择设备：** 在设备选择下拉菜单中选择"单元状态"
2. **参数展示：** 系统自动展开所有单元信息参数为具体状态参数
3. **参数选择：** 在参数控制面板中选择需要监控的状态参数
4. **数据查询：** 点击"查询数据"按钮获取历史数据
5. **图表查看：** 在右侧图表区域查看各状态参数的时间曲线

## 兼容性说明

- 仅对"单元状态"设备类型应用此二进制位解析功能
- 不影响其他设备类型的正常显示功能
- 保持与现有参数曲线界面的样式和功能一致性
- 支持所有现有的导出、搜索、时间筛选等功能

## 调试功能

可在浏览器控制台中使用 `debugAPI()` 函数查看详细的调试信息，包括：
- API配置信息
- 当前设备信息
- 参数模型展开过程
- 数据处理流程
