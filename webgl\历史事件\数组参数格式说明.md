# 数组参数格式说明

## 参数格式变更

已将时间参数的传输格式从JSON字符串改为数组参数格式：

### 修改前（JSON格式）
```
params={"beginTime":"2025-07-17 00:00:00","endTime":"2025-07-24 23:59:59"}
```

### 修改后（数组参数格式）
```
params[beginTime]=2025-07-17 00:00:00&params[endTime]=2025-07-24 23:59:59
```

## 完整URL示例

### 基本查询
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50
```

### 带事件类型筛选
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备
```

### 带时间范围筛选
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

### 完整筛选
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

## JavaScript实现

### 前端代码
```javascript
// 构建URL参数
const urlParams = new URLSearchParams();
urlParams.append('pageNum', params.pageNum);
urlParams.append('pageSize', params.pageSize);

// 添加事件类型筛选
if (params.alertName) {
    urlParams.append('alertName', params.alertName);
}

// 添加时间范围参数 - 使用数组格式
if (params.params) {
    if (params.params.beginTime) {
        urlParams.append('params[beginTime]', params.params.beginTime);
    }
    if (params.params.endTime) {
        urlParams.append('params[endTime]', params.params.endTime);
    }
}

// 生成最终URL
const apiUrl = baseUrl + '?' + urlParams.toString();
```

### 生成的URL示例
```
https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

## 后台接收方式

### Java Spring Boot
```java
// 方式1: 直接接收参数
@GetMapping("/iot/alertLog/list")
public ResponseEntity<?> getAlertLogList(
    @RequestParam int pageNum,
    @RequestParam int pageSize,
    @RequestParam(required = false) String alertName,
    @RequestParam(value = "params[beginTime]", required = false) String beginTime,
    @RequestParam(value = "params[endTime]", required = false) String endTime
) {
    // 直接使用beginTime和endTime
}

// 方式2: 使用Map接收params
@GetMapping("/iot/alertLog/list")
public ResponseEntity<?> getAlertLogList(
    @RequestParam int pageNum,
    @RequestParam int pageSize,
    @RequestParam(required = false) String alertName,
    @RequestParam(required = false) Map<String, String> params
) {
    String beginTime = params.get("beginTime");
    String endTime = params.get("endTime");
}
```

### Node.js Express
```javascript
app.get('/iot/alertLog/list', (req, res) => {
    const { pageNum, pageSize, alertName } = req.query;
    
    // 直接获取数组参数
    const beginTime = req.query['params[beginTime]'];
    const endTime = req.query['params[endTime]'];
    
    // 使用参数进行查询
    console.log('时间范围:', beginTime, 'to', endTime);
});
```

### PHP
```php
// 获取参数
$pageNum = $_GET['pageNum'];
$pageSize = $_GET['pageSize'];
$alertName = $_GET['alertName'] ?? null;

// 获取时间参数
$beginTime = $_GET['params']['beginTime'] ?? null;
$endTime = $_GET['params']['endTime'] ?? null;
```

## 优势

### 1. 更简单的后台处理
- 不需要JSON解析
- 直接作为普通参数接收
- 减少解析错误的可能性

### 2. 更好的URL可读性
- 参数结构清晰
- 便于调试和日志记录
- 支持标准的HTTP参数格式

### 3. 框架兼容性更好
- 大多数Web框架都原生支持数组参数
- 不需要额外的JSON处理库
- 更符合RESTful API规范

## 测试验证

### 浏览器开发者工具
在Network标签页中可以看到：
```
Query String Parameters:
pageNum: 1
pageSize: 50
alertName: 报警设备
params[beginTime]: 2025-07-17 00:00:00
params[endTime]: 2025-07-24 23:59:59
```

### curl测试命令
```bash
curl -X GET \
  'https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59' \
  -H 'Accept: application/json' \
  -H 'Authorization: Bearer [YOUR_TOKEN]'
```

### Postman测试
1. 设置请求方法为GET
2. 在Params标签页添加参数：
   - `pageNum`: 1
   - `pageSize`: 50
   - `alertName`: 报警设备
   - `params[beginTime]`: 2025-07-17 00:00:00
   - `params[endTime]`: 2025-07-24 23:59:59

## 注意事项

### 1. URL编码
时间参数中的空格会被编码为 `%20`，这是正常的URL编码行为。

### 2. 参数可选性
- `params[beginTime]` 和 `params[endTime]` 都是可选的
- 可以只传其中一个进行单边时间筛选
- 不传时表示不限制时间范围

### 3. 时间格式
确保时间格式为 `YYYY-MM-DD HH:mm:ss`，与后台期望的格式一致。

### 4. 兼容性
这种参数格式被所有主流Web框架和HTTP客户端支持，具有很好的兼容性。

## 总结

数组参数格式 `params[beginTime]` 和 `params[endTime]` 相比JSON字符串格式具有以下优势：
- ✅ 更简单的后台处理
- ✅ 更好的可读性和调试性
- ✅ 更广泛的框架支持
- ✅ 符合HTTP标准规范
- ✅ 减少解析错误风险

修改后的代码已经完全适配这种参数格式，可以正常工作。
