# 认证功能测试说明

## 认证信息配置

已成功添加认证头信息到API请求中：

### 认证配置
- **Header Key**: `Authorization`
- **Header Value**: `Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg5MWQ5ZWYxLTE3ZjItNGVjYy05ZTU2LTQyZTA0MGQyMmVmMiJ9.C45TcywB-9WRYyfu5a-kkm4C9T5q1TNLygCN4yN3mPm3xRBdd35yD1NNzcsWMpa5ARysZymC6BvFFpWdzXLRoA`

## 代码修改内容

### 1. API配置集中管理
```javascript
const API_CONFIG = {
    baseUrl: 'https://exdraw.qizhiyun.cc/prod-api',
    endpoints: {
        alertLog: '/iot/alertLog/list'
    },
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer [YOUR_TOKEN]'
    }
};
```

### 2. 请求头自动添加
```javascript
const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
        'Accept': 'application/json',
        'Authorization': API_CONFIG.headers.Authorization
    }
});
```

### 3. 认证错误处理
- **401错误**: "认证失败，请检查Authorization token是否有效"
- **403错误**: "权限不足，无法访问该接口"

### 4. Token验证功能
- 页面加载时自动验证token格式
- 检查JWT token的基本结构
- 验证token是否过期（如果包含exp字段）

## 测试步骤

### 1. 正常认证测试
1. 打开浏览器开发者工具（F12）
2. 切换到Console标签页
3. 打开历史事件页面
4. 观察控制台输出，应该看到：
   ```
   历史事件页面初始化开始...
   开始数据验证...
   Token payload: {...}
   认证token验证通过
   请求参数: {...}
   API响应数据: {...}
   ```

### 2. 网络请求验证
1. 在开发者工具中切换到Network标签页
2. 刷新页面
3. 找到对 `/iot/alertLog/list` 的GET请求
4. 点击查看请求详情
5. 在Request Headers中确认包含：
   ```
   Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
   Accept: application/json
   ```
6. 在Query String Parameters中确认包含：
   ```
   pageNum: 1
   pageSize: 50
   alertName: 报警设备 (如果有筛选)
   params[beginTime]: 2025-07-17 00:00:00 (如果有时间筛选)
   params[endTime]: 2025-07-24 23:59:59 (如果有时间筛选)
   ```

### 3. 认证失败测试
如需测试认证失败情况，可以临时修改token：
1. 在代码中将token改为无效值
2. 刷新页面
3. 应该看到401错误提示："认证失败，请检查Authorization token是否有效"

## 功能特点

### ✅ 安全性
- Token集中管理，便于更新维护
- 自动添加认证头，避免遗漏
- 专门的认证错误处理

### ✅ 可维护性
- 配置与业务逻辑分离
- 统一的API配置管理
- 清晰的错误提示信息

### ✅ 调试友好
- 详细的控制台日志
- Token有效性验证
- 网络请求状态监控

## 注意事项

1. **Token安全**: 当前token直接写在代码中，生产环境建议从安全的配置源获取
2. **Token过期**: 如果API返回401错误，可能需要更新token
3. **跨域问题**: 确保API服务器允许跨域请求
4. **HTTPS**: 建议在HTTPS环境下使用，保护token传输安全

## 故障排除

### 常见问题
1. **401 Unauthorized**: 检查token是否正确，是否过期
2. **403 Forbidden**: 检查用户权限，确认有访问该接口的权限
3. **CORS错误**: 联系后端开发者配置跨域访问
4. **网络错误**: 检查网络连接和API服务器状态

### 调试方法
1. 查看浏览器控制台的错误信息
2. 检查Network标签页的请求详情
3. 验证token格式和内容
4. 确认API接口地址是否正确

## 更新Token

如需更新认证token，只需修改 `API_CONFIG.headers.Authorization` 的值：

```javascript
API_CONFIG.headers.Authorization = 'Bearer [NEW_TOKEN]';
```

页面会自动使用新的token进行后续请求。
