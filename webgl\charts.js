/**
 * 白云电气设备数字孪生系统 - 图表配置文件
 * 包含所有图表的配置和初始化逻辑
 */

// 声明全局图表变量，以便在resize事件中访问
var chart1, chart2, chart3, chart4, harmonicCurrentChart, harmonicVoltageChart;
var svgCurrentChart, voltage10kvChart, gridCurrentChart;

// 全局数据存储对象，用于保存最新的MQTT数据
var latestElectricalData = {
  'HMI_32040': { value: null, name: 'SVG电流la', unit: 'A' }, // SVG电流Ia
  'HMI_32042': { value: null, name: 'SVG电流lb', unit: 'A' }, // SVG电流Ib
  'HMI_32044': { value: null, name: 'SVG电流lc', unit: 'A' }, // SVG电流Ic
  'HMI_32030': { value: null, name: '母线电压Uab', unit: 'kV' }, // 母线电压Uab
  'HMI_32032': { value: null, name: '母线电压Ubc', unit: 'kV' }, // 母线电压Ubc
  'HMI_32034': { value: null, name: '母线电压Uca', unit: 'kV' }, // 母线电压Uca
  'HMI_32046': { value: null, name: '网侧负载无功电流', unit: 'A' } // 网侧负载无功电流
};

// 图表配置常量
const CHART_CONFIG = {
  maxDataPoints: 60 // 图表最大数据点数
};

/**
 * 更新电气数据存储
 * 从MQTT数据中更新图表数据源
 * @param {Object} mqttData - MQTT接收到的数据
 */
function updateElectricalDataForCharts(mqttData) {
  if (!mqttData || !mqttData.properties) {
    console.warn('[图表数据更新] 无效的MQTT数据');
    return;
  }

  // 检查是否是第一次接收到数据
  const isFirstData = Object.values(latestElectricalData).every(data => data.value === null);

  // 更新数据存储
  Object.keys(mqttData.properties).forEach(propertyId => {
    if (latestElectricalData.hasOwnProperty(propertyId)) {
      const propertyData = mqttData.properties[propertyId];
      latestElectricalData[propertyId].value = propertyData.value;
      latestElectricalData[propertyId].isValid = propertyData.isValid;
      latestElectricalData[propertyId].timestamp = new Date();

      console.log(`[图表数据更新] 更新 ${latestElectricalData[propertyId].name}: ${propertyData.value} ${latestElectricalData[propertyId].unit}`);
    }
  });

  // 如果是第一次接收到数据，移除等待状态
  if (isFirstData) {
    removeChartsWaitingState();
    console.log('[图表数据更新] 首次接收到MQTT数据，移除等待状态');
  }

  // 触发图表更新
  updateRightPanelChartsFromRealData();
}

/**
 * 初始化所有图表
 * 页面加载后调用此函数
 */
function initCharts() {
  console.log('[图表初始化] 开始初始化所有图表');

  // 显示所有图表容器
  var chartContainers = document.querySelectorAll('.chart-container');
  chartContainers.forEach(function(container) {
    console.log('[图表初始化] 显示图表容器:', container.id);
    container.style.display = 'block';
  });

  // 初始化右侧面板的实时数据图表
  try {
    // 初始化谐波图表
    initHarmonicCurrentChart();
    initHarmonicVoltageChart();

    // 初始化右侧面板的三个新图表
    initSvgCurrentChart();
    initVoltage10kvChart();
    initGridCurrentChart();

    console.log('[图表初始化] 所有图表初始化完成');
  } catch (error) {
    console.error('[图表初始化] 图表初始化失败:', error);
  }

  console.log('[图表初始化] 所有图表初始化完成');
}

/**
 * 初始化设备运行状态图表（仪表盘）
 */
function initDeviceStatusChart() {
  console.log('[图表初始化] 开始初始化设备运行状态图表');
  
  chart1 = echarts.init(document.getElementById('chart1-content'));
  var option1 = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%',
      position: function (pos, params, el, elRect, size) {
        let obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true
    },
    series: [{
      name: '设备状态',
      type: 'gauge',
      detail: { formatter: '{value}%' },
      data: [{ value: 87, name: '运行状态' }],
      axisLine: {
        lineStyle: {
          color: [[0.3, '#ff4500'], [0.7, '#ffca28'], [1, '#00e676']]
        }
      }
    }]
  };
  chart1.setOption(option1);
  
  console.log('[图表初始化] 设备运行状态图表初始化完成');
}

/**
 * 初始化温度监测图表（折线图）
 */
function initTemperatureChart() {
  console.log('[图表初始化] 开始初始化温度监测图表');
  
  chart2 = echarts.init(document.getElementById('chart2-content'));
  var option2 = {
    tooltip: {
      trigger: 'axis',
      position: function (pos, params, el, elRect, size) {
        let obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} °C'
      }
    },
    series: [{
      name: '温度',
      type: 'line',
      data: [28, 32, 36, 38, 35, 30, 28],
      areaStyle: {},
      itemStyle: {
        color: '#ff7043'
      },
      lineStyle: {
        width: 3
      }
    }]
  };
  chart2.setOption(option2);
  
  console.log('[图表初始化] 温度监测图表初始化完成');
}

/**
 * 初始化电压监测图表（柱状图）
 */
function initVoltageChart() {
  console.log('[图表初始化] 开始初始化电压监测图表');
  
  chart3 = echarts.init(document.getElementById('chart3-content'));
  var option3 = {
    tooltip: {
      trigger: 'axis',
      position: function (pos, params, el, elRect, size) {
        let obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} V'
      }
    },
    series: [{
      name: '电压',
      type: 'bar',
      data: [220, 218, 221, 219, 220, 221, 217],
      itemStyle: {
        color: '#29b6f6'
      }
    }]
  };
  chart3.setOption(option3);
  
  console.log('[图表初始化] 电压监测图表初始化完成');
}

/**
 * 初始化系统负载图表（饼图）
 */
function initSystemLoadChart() {
  console.log('[图表初始化] 开始初始化系统负载图表');
  
  chart4 = echarts.init(document.getElementById('chart4-content'));
  var option4 = {
    tooltip: {
      trigger: 'item',
      position: function (pos, params, el, elRect, size) {
        let obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: '#fff'
      }
    },
    series: [{
      name: '系统负载',
      type: 'pie',
      radius: '70%',
      data: [
        { value: 40, name: '设备A' },
        { value: 25, name: '设备B' },
        { value: 20, name: '设备C' },
        { value: 15, name: '设备D' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart4.setOption(option4);
  
  console.log('[图表初始化] 系统负载图表初始化完成');
}

/**
 * 初始化谐波电流图表
 */
function initHarmonicCurrentChart() {
  console.log('[图表初始化] 开始初始化谐波电流图表');

  const chartElement = document.getElementById('harmonic-current-content');
  if (!chartElement) {
    console.warn('[图表初始化] 谐波电流图表容器未找到');
    return;
  }

  // 确保容器有尺寸
  if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
    console.warn('[图表初始化] 图表容器尺寸为0，延迟初始化');
    setTimeout(() => initHarmonicCurrentChart(), 500);
    return;
  }

  harmonicCurrentChart = echarts.init(chartElement);
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '谐波电流分析',
      textStyle: {
        color: '#00d4ff',
        fontSize: 14,
        fontWeight: 'bold'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#00d4ff',
          width: 2
        }
      },
      backgroundColor: 'rgba(26, 31, 46, 0.95)',
      borderColor: '#00d4ff',
      borderWidth: 2,
      borderRadius: 8,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      position: function (pos, params, el, elRect, size) {
        let obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true,
      formatter: function(params) {
        let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].name + '</div>';
        params.forEach(function(item) {
          result += '<div style="margin:2px 0;"><span style="color:' + item.color + ';font-size:14px;">●</span> ' +
                   '<span style="margin-left:5px;">' + item.seriesName + ': <strong>' + item.value + 'A</strong></span></div>';
        });
        return result;
      }
    },
    grid: {
      left: '10%',
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1次', '3次', '5次', '7次', '9次', '11次', '13次', '15次', '17次'],
      axisLine: {
        lineStyle: {
          color: '#00d4ff',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 12,
        fontWeight: 500
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00d4ff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '电流(A)',
      nameTextStyle: {
        color: '#00d4ff',
        fontSize: 12,
        fontWeight: 600
      },
      axisLine: {
        lineStyle: {
          color: '#00d4ff',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 11,
        formatter: '{value}A'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.15)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00d4ff'
        }
      }
    },
    series: [{
      name: '谐波电流',
      type: 'bar',
      data: [125.8, 15.2, 8.7, 4.3, 2.1, 1.8, 1.2, 0.9, 0.6],
      barWidth: '60%',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#66e0ff' },
          { offset: 0.5, color: '#00d4ff' },
          { offset: 1, color: '#0099cc' }
        ]),
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 212, 255, 0.3)',
        shadowBlur: 10,
        shadowOffsetY: 3
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#99f0ff' },
            { offset: 0.5, color: '#33ddff' },
            { offset: 1, color: '#00bbdd' }
          ]),
          shadowColor: 'rgba(0, 212, 255, 0.5)',
          shadowBlur: 15
        }
      },
      animationDelay: function (idx) {
        return idx * 100;
      }
    }],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 50;
    }
  };
  harmonicCurrentChart.setOption(option);

  console.log('[图表初始化] 谐波电流图表初始化完成');
}

/**
 * 初始化谐波电压图表
 */
function initHarmonicVoltageChart() {
  console.log('[图表初始化] 开始初始化谐波电压图表');

  const chartElement = document.getElementById('harmonic-voltage-content');
  if (!chartElement) {
    console.warn('[图表初始化] 谐波电压图表容器未找到');
    return;
  }

  // 确保容器有尺寸
  if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
    console.warn('[图表初始化] 图表容器尺寸为0，延迟初始化');
    setTimeout(() => initHarmonicVoltageChart(), 500);
    return;
  }

  harmonicVoltageChart = echarts.init(chartElement);
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '谐波电压分析',
      textStyle: {
        color: '#00ff88',
        fontSize: 14,
        fontWeight: 'bold'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#00ff88',
          width: 2
        }
      },
      backgroundColor: 'rgba(26, 31, 46, 0.95)',
      borderColor: '#00ff88',
      borderWidth: 2,
      borderRadius: 8,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      position: function (pos, params, el, elRect, size) {
        let obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true,
      formatter: function(params) {
        let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].name + '</div>';
        params.forEach(function(item) {
          result += '<div style="margin:2px 0;"><span style="color:' + item.color + ';font-size:14px;">●</span> ' +
                   '<span style="margin-left:5px;">' + item.seriesName + ': <strong>' + item.value + 'V</strong></span></div>';
        });
        return result;
      }
    },
    grid: {
      left: '10%',
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1次', '3次', '5次', '7次', '9次', '11次', '13次', '15次', '17次'],
      axisLine: {
        lineStyle: {
          color: '#00ff88',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 12,
        fontWeight: 500
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00ff88'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '电压(V)',
      nameTextStyle: {
        color: '#00ff88',
        fontSize: 12,
        fontWeight: 600
      },
      axisLine: {
        lineStyle: {
          color: '#00ff88',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 11,
        formatter: '{value}V'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 255, 136, 0.15)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00ff88'
        }
      }
    },
    series: [{
      name: '谐波电压',
      type: 'line',
      data: [10500, 315, 210, 147, 105, 84, 63, 42, 28],
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#00ff88' },
          { offset: 0.5, color: '#66ffaa' },
          { offset: 1, color: '#99ffcc' }
        ]),
        width: 4,
        shadowColor: 'rgba(0, 255, 136, 0.3)',
        shadowBlur: 10,
        shadowOffsetY: 3
      },
      itemStyle: {
        color: '#00ff88',
        borderColor: '#ffffff',
        borderWidth: 3,
        shadowColor: 'rgba(0, 255, 136, 0.5)',
        shadowBlur: 8
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(0, 255, 136, 0.4)' },
          { offset: 0.5, color: 'rgba(0, 255, 136, 0.2)' },
          { offset: 1, color: 'rgba(0, 255, 136, 0.05)' }
        ])
      },
      emphasis: {
        itemStyle: {
          color: '#66ffaa',
          borderColor: '#ffffff',
          borderWidth: 4,
          shadowColor: 'rgba(0, 255, 136, 0.8)',
          shadowBlur: 15
        },
        lineStyle: {
          width: 5
        }
      },
      animationDelay: function (idx) {
        return idx * 100;
      }
    }],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 50;
    }
  };
  harmonicVoltageChart.setOption(option);

  console.log('[图表初始化] 谐波电压图表初始化完成');
}

/**
 * 初始化SVG电流三相数据图表
 */
function initSvgCurrentChart() {
  console.log('[图表初始化] 开始初始化SVG电流三相数据图表');

  const chartElement = document.getElementById('svg-current-chart');
  if (!chartElement) {
    console.warn('[图表初始化] SVG电流图表容器未找到');
    return;
  }

  svgCurrentChart = echarts.init(chartElement);
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#00d4ff',
          width: 2
        }
      },
      backgroundColor: 'rgba(26, 31, 46, 0.95)',
      borderColor: '#00d4ff',
      borderWidth: 2,
      borderRadius: 8,
      textStyle: { color: '#ffffff', fontSize: 12 },
      padding: [8, 12],
      position: function (pos, params, el, elRect, size) {
        const obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true,
      formatter: function(params) {
        let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].axisValue + '</div>';
        params.forEach(function(item) {
          result += '<div style="display:flex;align-items:center;margin:3px 0;">';
          result += '<span style="display:inline-block;width:10px;height:10px;background-color:' + item.color + ';margin-right:8px;border-radius:50%;"></span>';
          result += '<span style="flex:1;">' + item.seriesName + ': </span>';
          result += '<span style="font-weight:bold;">' + item.value + 'A</span>';
          result += '</div>';
        });
        return result;
      }
    },
    legend: {
      data: ['A相', 'B相', 'C相'],
      textStyle: { color: '#b8c5d6', fontSize: 12 },
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      top: 10,
      right: '5%',
      padding: [5, 10],
      backgroundColor: 'rgba(26, 31, 46, 0.3)',
      borderRadius: 4,
      borderColor: 'rgba(0, 212, 255, 0.2)',
      borderWidth: 1
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#b8c5d6', fontSize: 10, margin: 10 }
    },
    yAxis: {
      type: 'value',
      name: '电流(A)',
      nameTextStyle: { color: '#00d4ff', fontSize: 10, padding: [0, 0, 5, 0] },
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#b8c5d6', fontSize: 10 },
      splitLine: { lineStyle: { color: 'rgba(0, 212, 255, 0.15)', type: 'dashed' } }
    },
    series: [
      {
        name: 'A相',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#ff4444', width: 2 },
        itemStyle: { color: '#ff4444', borderColor: '#ffffff', borderWidth: 1 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 68, 68, 0.3)' },
            { offset: 1, color: 'rgba(255, 68, 68, 0.05)' }
          ])
        }
      },
      {
        name: 'B相',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#44ff44', width: 2 },
        itemStyle: { color: '#44ff44', borderColor: '#ffffff', borderWidth: 1 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(68, 255, 68, 0.3)' },
            { offset: 1, color: 'rgba(68, 255, 68, 0.05)' }
          ])
        }
      },
      {
        name: 'C相',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#4444ff', width: 2 },
        itemStyle: { color: '#4444ff', borderColor: '#ffffff', borderWidth: 1 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(68, 68, 255, 0.3)' },
            { offset: 1, color: 'rgba(68, 68, 255, 0.05)' }
          ])
        }
      }
    ]
  };
  svgCurrentChart.setOption(option);

  console.log('[图表初始化] SVG电流三相数据图表初始化完成');
}

/**
 * 初始化10kV三相电压数据图表
 */
function initVoltage10kvChart() {
  console.log('[图表初始化] 开始初始化10kV三相电压数据图表');

  const chartElement = document.getElementById('voltage-10kv-chart');
  if (!chartElement) {
    console.warn('[图表初始化] 10kV电压图表容器未找到');
    return;
  }

  voltage10kvChart = echarts.init(chartElement);
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#00ff88',
          width: 2
        }
      },
      backgroundColor: 'rgba(26, 31, 46, 0.95)',
      borderColor: '#00ff88',
      borderWidth: 2,
      borderRadius: 8,
      textStyle: { color: '#ffffff', fontSize: 12 },
      padding: [8, 12],
      position: function (pos, params, el, elRect, size) {
        const obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true,
      formatter: function(params) {
        let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].axisValue + '</div>';
        params.forEach(function(item) {
          result += '<div style="display:flex;align-items:center;margin:3px 0;">';
          result += '<span style="display:inline-block;width:10px;height:10px;background-color:' + item.color + ';margin-right:8px;border-radius:50%;"></span>';
          result += '<span style="flex:1;">' + item.seriesName + ': </span>';
          result += '<span style="font-weight:bold;">' + item.value + 'kV</span>';
          result += '</div>';
        });
        return result;
      }
    },
    legend: {
      data: ['Uab', 'Ubc', 'Uca'],
      textStyle: { color: '#b8c5d6', fontSize: 12 },
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      top: 10,
      right: '5%',
      padding: [5, 10],
      backgroundColor: 'rgba(26, 31, 46, 0.3)',
      borderRadius: 4,
      borderColor: 'rgba(0, 255, 136, 0.2)',
      borderWidth: 1
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLine: { lineStyle: { color: '#00ff88' } },
      axisLabel: { color: '#b8c5d6', fontSize: 10, margin: 10 }
    },
    yAxis: {
      type: 'value',
      name: '电压(kV)',
      nameTextStyle: { color: '#00ff88', fontSize: 10, padding: [0, 0, 5, 0] },
      axisLine: { lineStyle: { color: '#00ff88' } },
      axisLabel: { color: '#b8c5d6', fontSize: 10 },
      splitLine: { lineStyle: { color: 'rgba(0, 255, 136, 0.15)', type: 'dashed' } }
    },
    series: [
      {
        name: 'Uab',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#ff4444', width: 2 },
        itemStyle: { color: '#ff4444', borderColor: '#ffffff', borderWidth: 1 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 68, 68, 0.3)' },
            { offset: 1, color: 'rgba(255, 68, 68, 0.05)' }
          ])
        }
      },
      {
        name: 'Ubc',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#44ff44', width: 2 },
        itemStyle: { color: '#44ff44', borderColor: '#ffffff', borderWidth: 1 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(68, 255, 68, 0.3)' },
            { offset: 1, color: 'rgba(68, 255, 68, 0.05)' }
          ])
        }
      },
      {
        name: 'Uca',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#4444ff', width: 2 },
        itemStyle: { color: '#4444ff', borderColor: '#ffffff', borderWidth: 1 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(68, 68, 255, 0.3)' },
            { offset: 1, color: 'rgba(68, 68, 255, 0.05)' }
          ])
        }
      }
    ]
  };
  voltage10kvChart.setOption(option);

  console.log('[图表初始化] 10kV三相电压数据图表初始化完成');
}

/**
 * 初始化电网电流两相数据图表
 */
function initGridCurrentChart() {
  console.log('[图表初始化] 开始初始化电网电流两相数据图表');

  const chartElement = document.getElementById('grid-current-chart');
  if (!chartElement) {
    console.warn('[图表初始化] 电网电流图表容器未找到');
    return;
  }

  gridCurrentChart = echarts.init(chartElement);
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#ffaa00',
          width: 2
        }
      },
      backgroundColor: 'rgba(26, 31, 46, 0.95)',
      borderColor: '#ffaa00',
      borderWidth: 2,
      borderRadius: 8,
      textStyle: { color: '#ffffff', fontSize: 12 },
      padding: [8, 12],
      position: function (pos, params, el, elRect, size) {
        const obj = { top: 10 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      confine: true,
      formatter: function(params) {
        let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].axisValue + '</div>';
        params.forEach(function(item) {
          result += '<div style="display:flex;align-items:center;margin:3px 0;">';
          result += '<span style="display:inline-block;width:10px;height:10px;background-color:' + item.color + ';margin-right:8px;border-radius:50%;"></span>';
          result += '<span style="flex:1;">' + item.seriesName + ': </span>';
          result += '<span style="font-weight:bold;">' + item.value + 'A</span>';
          result += '</div>';
        });
        return result;
      }
    },
    legend: {
      data: ['网侧负载无功电流'],
      textStyle: { color: '#b8c5d6', fontSize: 12 },
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      top: 10,
      right: '5%',
      padding: [5, 10],
      backgroundColor: 'rgba(26, 31, 46, 0.3)',
      borderRadius: 4,
      borderColor: 'rgba(255, 170, 0, 0.2)',
      borderWidth: 1
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLine: { lineStyle: { color: '#ffaa00' } },
      axisLabel: { color: '#b8c5d6', fontSize: 10, margin: 10 }
    },
    yAxis: {
      type: 'value',
      name: '电流(A)',
      nameTextStyle: { color: '#ffaa00', fontSize: 10, padding: [0, 0, 5, 0] },
      axisLine: { lineStyle: { color: '#ffaa00' } },
      axisLabel: { color: '#b8c5d6', fontSize: 10 },
      splitLine: { lineStyle: { color: 'rgba(255, 170, 0, 0.15)', type: 'dashed' } }
    },
    series: [
      {
        name: '网侧负载无功电流',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 170, 0, 0.3)' },
            { offset: 1, color: 'rgba(255, 170, 0, 0.05)' }
          ])
        },
        lineStyle: { color: '#ffaa00', width: 3 },
        itemStyle: { 
          color: '#ffaa00',
          borderColor: '#ffffff',
          borderWidth: 1,
          shadowColor: 'rgba(255, 170, 0, 0.5)',
          shadowBlur: 5
        }
      }
    ]
  };
  gridCurrentChart.setOption(option);

  console.log('[图表初始化] 电网电流两相数据图表初始化完成');
}

/**
 * 调整所有图表大小
 * 在窗口大小变化时调用
 */
function resizeAllCharts() {
  // 添加延迟以确保DOM已完全更新
  setTimeout(function() {
    if (chart1) chart1.resize();
    if (chart2) chart2.resize();
    if (chart3) chart3.resize();
    if (chart4) chart4.resize();
    if (harmonicCurrentChart) harmonicCurrentChart.resize();
    if (harmonicVoltageChart) harmonicVoltageChart.resize();
    if (svgCurrentChart) svgCurrentChart.resize();
    if (voltage10kvChart) voltage10kvChart.resize();
    if (gridCurrentChart) gridCurrentChart.resize();
    
    // 重新设置图例位置，确保在调整大小后仍然可见
    if (svgCurrentChart) {
      let option = svgCurrentChart.getOption();
      option.legend.right = '5%';
      svgCurrentChart.setOption(option, false);
    }
    if (voltage10kvChart) {
      let option = voltage10kvChart.getOption();
      option.legend.right = '5%';
      voltage10kvChart.setOption(option, false);
    }
    if (gridCurrentChart) {
      let option = gridCurrentChart.getOption();
      option.legend.right = '5%';
      gridCurrentChart.setOption(option, false);
    }
  }, 200);
}

/**
 * 更新谐波图表数据
 * 模拟实时数据更新
 */
function updateHarmonicCharts() {
  // 更新谐波电流数据
  if (harmonicCurrentChart) {
    const currentData = [
      125.8 + (Math.random() - 0.5) * 5,
      15.2 + (Math.random() - 0.5) * 2,
      8.7 + (Math.random() - 0.5) * 1,
      4.3 + (Math.random() - 0.5) * 0.5,
      2.1 + (Math.random() - 0.5) * 0.3,
      1.8 + (Math.random() - 0.5) * 0.2,
      1.2 + (Math.random() - 0.5) * 0.2,
      0.9 + (Math.random() - 0.5) * 0.1,
      0.6 + (Math.random() - 0.5) * 0.1
    ];

    harmonicCurrentChart.setOption({
      series: [{
        data: currentData.map(val => Math.max(0, val.toFixed(1)))
      }]
    });
  }

  // 更新谐波电压数据
  if (harmonicVoltageChart) {
    const voltageData = [
      10500 + (Math.random() - 0.5) * 200,
      315 + (Math.random() - 0.5) * 30,
      210 + (Math.random() - 0.5) * 20,
      147 + (Math.random() - 0.5) * 15,
      105 + (Math.random() - 0.5) * 10,
      84 + (Math.random() - 0.5) * 8,
      63 + (Math.random() - 0.5) * 6,
      42 + (Math.random() - 0.5) * 4,
      28 + (Math.random() - 0.5) * 3
    ];

    harmonicVoltageChart.setOption({
      series: [{
        data: voltageData.map(val => Math.max(0, val.toFixed(0)))
      }]
    });
  }
}

/**
 * 更新右侧面板图表数据（使用真实数据）
 */
function updateRightPanelChartsFromRealData() {
  updateSvgCurrentChart();
  updateVoltage10kvChart();
  updateGridCurrentChart();
}

/**
 * 更新右侧面板图表数据（保留原函数名以兼容现有代码）
 * @deprecated 建议使用 updateRightPanelChartsFromRealData()
 */
function updateRightPanelCharts() {
  // 不再使用模拟数据，改为检查是否有真实数据
  const hasRealData = Object.values(latestElectricalData).some(data => data.value !== null);

  if (hasRealData) {
    updateRightPanelChartsFromRealData();
  } else {
    console.log('[图表更新] 等待真实MQTT数据，跳过图表更新');
  }
}

/**
 * 更新SVG电流三相数据图表（使用真实数据）
 */
function updateSvgCurrentChart() {
  if (!svgCurrentChart) return;

  const now = new Date();
  const timeStr = now.toLocaleTimeString();

  // 从真实数据中获取SVG三相电流数据
  const iaValue = latestElectricalData['HMI_32040'].value; // SVG电流la
  const ibValue = latestElectricalData['HMI_32042'].value; // SVG电流lb
  const icValue = latestElectricalData['HMI_32044'].value; // SVG电流lc

  // 如果没有真实数据，不更新图表
  if (iaValue === null || ibValue === null || icValue === null) {
    console.log('[SVG电流图表] 等待真实数据...');
    return;
  }

  const option = svgCurrentChart.getOption();
  const xData = option.xAxis[0].data;
  const seriesData = option.series;

  // 保持配置的最大数据点数
  if (xData.length >= CHART_CONFIG.maxDataPoints) {
    xData.shift();
    seriesData[0].data.shift();
    seriesData[1].data.shift();
    seriesData[2].data.shift();
  }

  xData.push(timeStr);
  seriesData[0].data.push(parseFloat(iaValue).toFixed(1));
  seriesData[1].data.push(parseFloat(ibValue).toFixed(1));
  seriesData[2].data.push(parseFloat(icValue).toFixed(1));

  svgCurrentChart.setOption({
    xAxis: { data: xData },
    series: seriesData
  });

  console.log(`[SVG电流图表] 更新数据 - Ia: ${iaValue}A, Ib: ${ibValue}A, Ic: ${icValue}A`);
}

/**
 * 更新10kV三相电压数据图表（使用真实数据）
 */
function updateVoltage10kvChart() {
  if (!voltage10kvChart) return;

  const now = new Date();
  const timeStr = now.toLocaleTimeString();

  // 从真实数据中获取母线三相电压数据
  const uabValue = latestElectricalData['HMI_32030'].value; // 母线电压Uab
  const ubcValue = latestElectricalData['HMI_32032'].value; // 母线电压Ubc
  const ucaValue = latestElectricalData['HMI_32034'].value; // 母线电压Uca

  // 如果没有真实数据，不更新图表
  if (uabValue === null || ubcValue === null || ucaValue === null) {
    console.log('[母线电压图表] 等待真实数据...');
    return;
  }

  const option = voltage10kvChart.getOption();
  const xData = option.xAxis[0].data;
  const seriesData = option.series;

  // 保持最多60个数据点
  if (xData.length >= 60) {
    xData.shift();
    seriesData[0].data.shift();
    seriesData[1].data.shift();
    seriesData[2].data.shift();
  }

  xData.push(timeStr);
  seriesData[0].data.push(parseFloat(uabValue).toFixed(2));
  seriesData[1].data.push(parseFloat(ubcValue).toFixed(2));
  seriesData[2].data.push(parseFloat(ucaValue).toFixed(2));

  voltage10kvChart.setOption({
    xAxis: { data: xData },
    series: seriesData
  });

  console.log(`[母线电压图表] 更新数据 - Uab: ${uabValue}kV, Ubc: ${ubcValue}kV, Uca: ${ucaValue}kV`);
}

/**
 * 更新网侧负载无功电流图表（使用真实数据）
 */
function updateGridCurrentChart() {
  if (!gridCurrentChart) return;

  const now = new Date();
  const timeStr = now.toLocaleTimeString();

  // 从真实数据中获取网侧负载无功电流数据
  const gridReactiveCurrentValue = latestElectricalData['HMI_32046'].value; // 网侧负载无功电流

  // 如果没有真实数据，不更新图表
  if (gridReactiveCurrentValue === null) {
    console.log('[网侧负载无功电流图表] 等待真实数据...');
    return;
  }

  const option = gridCurrentChart.getOption();
  const xData = option.xAxis[0].data;
  const seriesData = option.series;

  // 保持配置的最大数据点数
  if (xData.length >= CHART_CONFIG.maxDataPoints) {
    xData.shift();
    seriesData[0].data.shift();
  }

  xData.push(timeStr);
  seriesData[0].data.push(parseFloat(gridReactiveCurrentValue).toFixed(1));

  gridCurrentChart.setOption({
    xAxis: { data: xData },
    series: seriesData
  });

  console.log(`[网侧负载无功电流图表] 更新数据 - 无功电流: ${gridReactiveCurrentValue}A`);
}

// 移除定时器驱动，使用MQTT实时数据更新

// 右侧面板图表现在由MQTT数据驱动，不再使用定时器
// 当接收到MQTT数据时，会调用 updateElectricalDataForCharts() 函数来更新图表
console.log('[图表系统] 右侧面板图表已切换为MQTT数据驱动模式');

/**
 * 初始化图表等待数据状态
 * 在没有MQTT数据时显示等待提示
 */
function initChartsWaitingState() {
  // 为每个图表添加等待数据的提示
  const chartIds = ['svg-current-chart', 'voltage-10kv-chart', 'grid-current-chart'];

  chartIds.forEach(chartId => {
    const chartElement = document.getElementById(chartId);
    if (chartElement) {
      // 创建等待数据的提示层
      const waitingDiv = document.createElement('div');
      waitingDiv.className = 'chart-waiting-overlay';
      waitingDiv.innerHTML = `
        <div class="waiting-content">
          <i class="fas fa-clock"></i>
          <p>等待MQTT数据...</p>
        </div>
      `;
      waitingDiv.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(10, 15, 28, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ff4444;
        font-size: 14px;
        z-index: 10;
      `;

      // 确保父容器有相对定位
      chartElement.parentElement.style.position = 'relative';
      chartElement.parentElement.appendChild(waitingDiv);
    }
  });
}

/**
 * 移除图表等待状态
 */
function removeChartsWaitingState() {
  const waitingOverlays = document.querySelectorAll('.chart-waiting-overlay');
  waitingOverlays.forEach(overlay => {
    overlay.remove();
  });
}

// 页面加载后初始化等待状态
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initChartsWaitingState, 1000); // 延迟1秒确保图表已初始化
    // 添加窗口大小调整事件监听器
    window.addEventListener('resize', resizeAllCharts);
    // 初始调用一次以确保图表正确显示
    setTimeout(resizeAllCharts, 500);
  });
} else {
  setTimeout(initChartsWaitingState, 1000);
  // 添加窗口大小调整事件监听器
  window.addEventListener('resize', resizeAllCharts);
  // 初始调用一次以确保图表正确显示
  setTimeout(resizeAllCharts, 500);
}
