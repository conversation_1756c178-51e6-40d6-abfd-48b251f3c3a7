/**
 * MQTT 电气系统实时数据更新功能演示脚本
 * 白云电气设备数字孪生系统
 */

// 演示数据生成器
class ElectricalDataDemo {
    constructor() {
        this.isRunning = false;
        this.interval = null;
        this.dataSequence = 0;
        
        // 模拟设备状态变化序列
        this.statusSequences = {
            normal: [
                { running: 1, fault: 0, ready: 1, standby: 0, highVoltage: 1 },
                { running: 1, fault: 0, ready: 1, standby: 0, highVoltage: 1 },
                { running: 1, fault: 0, ready: 1, standby: 0, highVoltage: 1 }
            ],
            startup: [
                { running: 0, fault: 0, ready: 1, standby: 0, highVoltage: 0 },
                { running: 0, fault: 0, ready: 1, standby: 0, highVoltage: 1 },
                { running: 1, fault: 0, ready: 1, standby: 0, highVoltage: 1 }
            ],
            fault: [
                { running: 1, fault: 0, ready: 1, standby: 0, highVoltage: 1 },
                { running: 0, fault: 1, ready: 0, standby: 0, highVoltage: 0 },
                { running: 0, fault: 1, ready: 0, standby: 1, highVoltage: 0 }
            ],
            recovery: [
                { running: 0, fault: 1, ready: 0, standby: 1, highVoltage: 0 },
                { running: 0, fault: 0, ready: 1, standby: 0, highVoltage: 0 },
                { running: 1, fault: 0, ready: 1, standby: 0, highVoltage: 1 }
            ]
        };
        
        this.currentSequence = 'normal';
        this.sequenceIndex = 0;
    }

    /**
     * 开始演示
     * @param {number} intervalMs - 更新间隔（毫秒）
     */
    start(intervalMs = 3000) {
        if (this.isRunning) {
            console.log('演示已在运行中');
            return;
        }

        this.isRunning = true;
        console.log('开始 MQTT 电气系统数据演示...');

        // 立即发送一次数据
        this.generateAndSendData();
    }

    /**
     * 停止演示
     */
    stop() {
        if (!this.isRunning) {
            console.log('演示未在运行');
            return;
        }

        this.isRunning = false;
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        console.log('MQTT 电气系统数据演示已停止');
    }

    /**
     * 切换演示场景
     * @param {string} scenario - 场景名称：normal, startup, fault, recovery
     */
    switchScenario(scenario) {
        if (this.statusSequences[scenario]) {
            this.currentSequence = scenario;
            this.sequenceIndex = 0;
            console.log(`切换到演示场景: ${scenario}`);
        } else {
            console.error(`未知的演示场景: ${scenario}`);
        }
    }

    /**
     * 生成并发送模拟数据
     */
    generateAndSendData() {
        const data = this.generateElectricalData();
        console.log('生成演示数据:', data);
        
        // 如果存在 MQTT 管理器，直接处理数据
        if (typeof mqttElectricalManager !== 'undefined' && mqttElectricalManager) {
            mqttElectricalManager.processElectricalData(data);
        } else {
            console.warn('MQTT 电气系统管理器未初始化');
        }
        
        // 更新序列索引
        this.sequenceIndex = (this.sequenceIndex + 1) % this.statusSequences[this.currentSequence].length;
        this.dataSequence++;
    }

    /**
     * 生成电气系统模拟数据
     * @returns {Object} 模拟的电气系统数据
     */
    generateElectricalData() {
        const sequence = this.statusSequences[this.currentSequence];
        const currentStatus = sequence[this.sequenceIndex];
        
        // 添加一些随机变化
        const addNoise = (value, probability = 0.1) => {
            return Math.random() < probability ? (1 - value) : value;
        };

        return {
            properties: {
                'HMI_30039_12': addNoise(0, 0.05), // 复位2
                'HMI_30039_3': addNoise(0, 0.05),  // 复位
                'HMI_30039_2': addNoise(0, 0.1),   // 单元自检
                'HMI_30039_4': addNoise(currentStatus.ready, 0.05),     // 就绪
                'HMI_30039_5': addNoise(currentStatus.running, 0.05),   // 运行
                'HMI_30039_6': addNoise(currentStatus.fault, 0.02),     // 故障
                'HMI_30039_9': addNoise(currentStatus.standby, 0.05),   // 备用
                'HMI_30039_11': addNoise(currentStatus.fault, 0.02),    // 故障2
                'HMI_30039_10': addNoise(0, 0.03), // 合高压等待
                'HMI_30039_7': addNoise(currentStatus.highVoltage, 0.05) // 高压
            },
            functions: [],
            events: this.generateEvents(currentStatus),
            timestamp: new Date().toISOString(),
            deviceId: 'D19QBHKRZ791U',
            sequenceInfo: {
                scenario: this.currentSequence,
                index: this.sequenceIndex,
                dataSequence: this.dataSequence
            }
        };
    }

    /**
     * 生成事件数据
     * @param {Object} status - 当前状态
     * @returns {Array} 事件数组
     */
    generateEvents(status) {
        const events = [];
        
        if (status.fault) {
            events.push({
                type: 'fault',
                message: '系统故障检测',
                severity: 'high',
                timestamp: new Date().toISOString()
            });
        }
        
        if (status.running && !status.fault) {
            events.push({
                type: 'normal',
                message: '系统正常运行',
                severity: 'info',
                timestamp: new Date().toISOString()
            });
        }
        
        return events;
    }

    /**
     * 获取演示状态
     * @returns {Object} 演示状态信息
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            currentSequence: this.currentSequence,
            sequenceIndex: this.sequenceIndex,
            dataSequence: this.dataSequence,
            availableScenarios: Object.keys(this.statusSequences)
        };
    }
}

// 创建全局演示实例
let electricalDataDemo = null;

/**
 * 初始化演示功能
 */
function initializeDemo() {
    electricalDataDemo = new ElectricalDataDemo();
    console.log('MQTT 电气系统数据演示初始化完成');
    
    // 添加全局控制函数
    window.startElectricalDemo = function(interval = 3000) {
        if (electricalDataDemo) {
            electricalDataDemo.start(interval);
        }
    };
    
    window.stopElectricalDemo = function() {
        if (electricalDataDemo) {
            electricalDataDemo.stop();
        }
    };
    
    window.switchDemoScenario = function(scenario) {
        if (electricalDataDemo) {
            electricalDataDemo.switchScenario(scenario);
        }
    };
    
    window.getDemoStatus = function() {
        return electricalDataDemo ? electricalDataDemo.getStatus() : null;
    };
    
    // 在控制台显示可用命令
    console.log(`
=== MQTT 电气系统数据演示控制命令 ===
startElectricalDemo(interval)  - 开始演示（可选参数：更新间隔毫秒）
stopElectricalDemo()          - 停止演示
switchDemoScenario(scenario)  - 切换场景：'normal', 'startup', 'fault', 'recovery'
getDemoStatus()              - 获取演示状态

示例：
startElectricalDemo(2000)     - 每2秒更新一次数据
switchDemoScenario('fault')   - 切换到故障场景
stopElectricalDemo()         - 停止演示
    `);
}

/**
 * 自动演示序列
 * 按顺序执行不同场景的演示
 */
function runAutoDemo() {
    if (!electricalDataDemo) {
        console.error('演示未初始化');
        return;
    }
    
    const scenarios = ['startup', 'normal', 'fault', 'recovery', 'normal'];
    let currentIndex = 0;
    
    console.log('开始自动演示序列...');
    electricalDataDemo.start(2000);
    
    // 移除定时器驱动，使用事件或手动触发
    console.log('自动演示序列已移除定时器驱动');
    
    // 添加停止自动演示的函数
    window.stopAutoDemo = function() {
        clearInterval(switchInterval);
        electricalDataDemo.stop();
        console.log('自动演示已停止');
    };
}

// 页面加载完成后初始化演示
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDemo);
    } else {
        initializeDemo();
    }
    
    // 添加自动演示函数到全局
    window.runAutoDemo = runAutoDemo;
}

// 导出演示类（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ElectricalDataDemo;
} else if (typeof window !== 'undefined') {
    window.ElectricalDataDemo = ElectricalDataDemo;
}
